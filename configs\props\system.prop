# AEE
ro.vendor.have_aee_feature=1

# Kernel
ro.kernel.zio=38,108,105,16

# Audio
ro.audio.silent=0
ro.audio.usb.period_us=16000
ro.camera.sound.forced=0
ro.config.vc_call_vol_steps=7

# Brand
Build.BRAND=MTK

# Dalvik
dalvik.vm.ps-min-first-save-ms=150000
dalvik.vm.image-dex2oat-Xms=64m
dalvik.vm.image-dex2oat-Xmx=64m
dalvik.vm.dex2oat-Xms=64m
dalvik.vm.dex2oat-Xmx=512m
dalvik.vm.usejit=true
dalvik.vm.usejitprofiles=true
dalvik.vm.dexopt.secondary=true
dalvik.vm.dexopt.thermal-cutoff=2
dalvik.vm.appimageformat=lz4
ro.dalvik.vm.native.bridge=0
dalvik.vm.dex2oat-resolve-startup-strings=true
dalvik.vm.dex2oat-max-image-block-size=524288
dalvik.vm.minidebuginfo=true
dalvik.vm.dex2oat-minidebuginfo=true

# Dexopt
pm.dexopt.first-boot=quicken
pm.dexopt.boot=verify
pm.dexopt.boot-after-ota=verify
pm.dexopt.post-boot=extract
pm.dexopt.install=speed-profile
pm.dexopt.install-fast=skip
pm.dexopt.install-bulk=speed-profile
pm.dexopt.install-bulk-secondary=verify
pm.dexopt.install-bulk-downgraded=verify
pm.dexopt.install-bulk-secondary-downgraded=extract
pm.dexopt.bg-dexopt=speed-profile
pm.dexopt.ab-ota=speed-profile
pm.dexopt.inactive=verify
pm.dexopt.cmdline=verify
pm.dexopt.shared=speed

# Display
ro.opengles.version=196610 
sys.brightness.disable_gamma_conversion=true

# IMS
persist.dbg.volte_avail_ovr=1 
persist.dbg.vt_avail_ovr=1
persist.dbg.wfc_avail_ovr=1

# IPO
sys.ipo.pwrdncap=2
sys.ipo.disable=1

# iorapd
ro.iorapd.enable=false

# Media
ro.vendor.mtk_flv_playback_support=1
media.stagefright.thumbnail.prefer_hw_codecs=true
vendor.mtk_thumbnail_optimization=true

# Memory
ro.config.per_app_memcg=false

# Misc
persist.vendor.pms_removable=1
persist.vendor.vzw_device_type=0
ro.vendor.mtk_gwsd_support=1
persist.vendor.mdlog.flush_log_ratio=0
ro.mediatek.version.branch=alps-mp-t0.mssi1.tc16sp-pr1
ro.mediatek.version.release=alps-mp-t0.mp1.tc16sp-pr1-V1
ro.mediatek.version.build.branch=
ro.vendor.mtk_omacp_support=1
ro.vendor.mtk_cta_set=1
ro.vendor.qti.va_aosp.support=0
debug.atrace.tags.enableflags=0
persist.traced.enable=1
persist.vendor.vzw_device_type=0
ro.vendor.customer_logpath=/data
persist.vendor.mdlog.flush_log_ratio=0
vendor.mtk.hbt.enabled=0
hbt.debug=off
ro.base_build=noah
ro.vendor.mtk_gwsd_support=1
ro.vendor.mtk_power_off_alarm_test=1

# Perf
ro.mtk_perf_simple_start_win=1
ro.mtk_perf_fast_start_win=1
ro.mtk_perf_response_time=1

# RIL
vendor.rild.libpath=mtk-ril.so
vendor.rild.libargs=-d /dev/ttyC0
ro.vendor.mtk_telephony_add_on_policy=0
ro.telephony.sim.count=2
persist.radio.multisim.config=dsds
persist.radio.hvolte.enable=1

# Storage
persist.sys.fuse.passthrough.enable=true

# USB
ro.sys.usb.mtp.whql.enable=0
ro.sys.usb.storage.type=mtp
ro.sys.usb.bicr=no
ro.sys.usb.charging.only=yes

# WiFi
wifi.interface=wlan0
wifi.tethering.interface=ap0
wifi.direct.interface=p2p0
ro.mediatek.wlan.wsc=1
ro.mediatek.wlan.p2p=1
mediatek.wlan.ctia=0
persist.vendor.wfc.sys_wfc_support=1

# Zygote
ro.zygote.preload.enable=0

# Radio VoNR Calling
persist.radio.is_vonr_enabled_0=true
persist.radio.is_vonr_enabled_1=true
persist.vendor.vonr_setting_support=1

#ADB
persist.service.adb.enable=1                                                    
persist.service.debuggable=1
persist.sys.usb.config=mtp,adb

#Camera
persist.camera.HAL3.enabled=1
persist.camera.eis.enabled=1
ro.camera.sound.forced=0
