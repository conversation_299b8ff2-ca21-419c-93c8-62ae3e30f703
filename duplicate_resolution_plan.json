﻿{
    "Summary":  {
                    "BackupDir":  "sepolicy_backup_20250721_084042",
                    "TotalProcessed":  134,
                    "CriticalFiles":  36,
                    "FilesToRemove":  88,
                    "FilesToBackup":  46
                },
    "CriticalReview":  [
                           {
                               "File":  "vendor\\file_contexts",
                               "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1383,
                               "CompareSize":  7603,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\genfs_contexts",
                               "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  3405,
                               "CompareSize":  10601,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\hwservice.te",
                               "BaseFile":  "basic\\debug\\non_plat\\hwservice.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  52,
                               "CompareSize":  496,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\hwservice_contexts",
                               "BaseFile":  "basic\\debug\\non_plat\\hwservice_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  71,
                               "CompareSize":  1892,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\property_contexts",
                               "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  559,
                               "CompareSize":  3263,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\property_contexts",
                               "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  559,
                               "CompareSize":  1165,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\system_app.te",
                               "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  231,
                               "CompareSize":  681,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\vendor_init.te",
                               "BaseFile":  "basic\\debug\\non_plat\\vendor_init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  202,
                               "CompareSize":  792,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file_contexts",
                               "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1595,
                               "CompareSize":  7603,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\init.te",
                               "BaseFile":  "basic\\debug\\plat_private\\init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  43,
                               "CompareSize":  91,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\init.te",
                               "BaseFile":  "basic\\debug\\plat_private\\init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  43,
                               "CompareSize":  1367,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\property_contexts",
                               "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  336,
                               "CompareSize":  3263,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\property_contexts",
                               "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  336,
                               "CompareSize":  1165,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "public\\property.te",
                               "BaseFile":  "basic\\debug\\plat_public\\property.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  106,
                               "CompareSize":  273,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\property.te",
                               "BaseFile":  "basic\\debug\\plat_public\\property.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  106,
                               "CompareSize":  240,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\hal_power_default.te",
                               "BaseFile":  "basic\\non_plat\\hal_power_default.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  363,
                               "CompareSize":  2281,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\service_contexts",
                               "BaseFile":  "basic\\non_plat\\service_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  75,
                               "CompareSize":  270,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file_contexts",
                               "BaseFile":  "basic\\plat_private\\file_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1728,
                               "CompareSize":  7603,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\genfs_contexts",
                               "BaseFile":  "basic\\plat_private\\genfs_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1258,
                               "CompareSize":  10601,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\init.te",
                               "BaseFile":  "basic\\plat_private\\init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  569,
                               "CompareSize":  1367,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\vendor_init.te",
                               "BaseFile":  "basic\\plat_private\\vendor_init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  99,
                               "CompareSize":  792,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file.te",
                               "BaseFile":  "basic\\plat_public\\file.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  429,
                               "CompareSize":  1979,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file.te",
                               "BaseFile":  "bsp\\debug\\non_plat\\file.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  337,
                               "CompareSize":  1979,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\surfaceflinger.te",
                               "BaseFile":  "bsp\\debug\\non_plat\\surfaceflinger.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  51,
                               "CompareSize":  177,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\system_app.te",
                               "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  262,
                               "CompareSize":  681,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\system_server.te",
                               "BaseFile":  "bsp\\debug\\non_plat\\system_server.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  229,
                               "CompareSize":  481,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\genfs_contexts",
                               "BaseFile":  "bsp\\non_plat\\genfs_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  2941,
                               "CompareSize":  10601,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file_contexts",
                               "BaseFile":  "bsp\\plat_private\\file_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1633,
                               "CompareSize":  7603,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\init.te",
                               "BaseFile":  "bsp\\plat_private\\init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  324,
                               "CompareSize":  1367,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\vendor_init.te",
                               "BaseFile":  "bsp\\plat_private\\vendor_init.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  244,
                               "CompareSize":  792,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file.te",
                               "BaseFile":  "bsp\\plat_public\\file.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  342,
                               "CompareSize":  1979,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\property_contexts",
                               "BaseFile":  "legacy\\non_plat\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  424,
                               "CompareSize":  3263,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\property_contexts",
                               "BaseFile":  "legacy\\non_plat\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  424,
                               "CompareSize":  1165,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file.te",
                               "BaseFile":  "modem\\file.te",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  230,
                               "CompareSize":  1979,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "vendor\\file_contexts",
                               "BaseFile":  "modem\\file_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  1787,
                               "CompareSize":  7603,
                               "Action":  "Manual Review"
                           },
                           {
                               "File":  "private\\property_contexts",
                               "BaseFile":  "modem\\property_contexts",
                               "Reason":  "Compare file is larger - needs manual review",
                               "BaseSize":  809,
                               "CompareSize":  3263,
                               "Action":  "Manual Review"
                           }
                       ],
    "ToBackup":  [
                     {
                         "File":  "vendor\\device.te",
                         "BaseFile":  "basic\\debug\\non_plat\\device.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  115,
                         "CompareSize":  189,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\file.te",
                         "BaseFile":  "basic\\debug\\non_plat\\file.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  2371,
                         "CompareSize":  1979,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "BaseFile":  "basic\\debug\\non_plat\\mtk_hal_camera.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  1150,
                         "CompareSize":  1139,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "basic\\debug\\non_plat\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  658,
                         "CompareSize":  273,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "basic\\debug\\non_plat\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  658,
                         "CompareSize":  240,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  231,
                         "CompareSize":  165,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "basic\\debug\\non_plat\\system_server.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  535,
                         "CompareSize":  481,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\kernel.te",
                         "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  194,
                         "CompareSize":  261,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  194,
                         "CompareSize":  97,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "basic\\debug\\plat_private\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  475,
                         "CompareSize":  273,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "basic\\debug\\plat_private\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  475,
                         "CompareSize":  240,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\radio.te",
                         "BaseFile":  "basic\\debug\\plat_private\\radio.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  215,
                         "CompareSize":  241,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "basic\\debug\\plat_private\\system_server.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  465,
                         "CompareSize":  481,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\hwservice_contexts",
                         "BaseFile":  "basic\\non_plat\\hwservice_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  3450,
                         "CompareSize":  1892,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "BaseFile":  "basic\\non_plat\\priv_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  377,
                         "CompareSize":  344,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "BaseFile":  "basic\\non_plat\\system_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  1464,
                         "CompareSize":  681,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "BaseFile":  "basic\\non_plat\\untrusted_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  411,
                         "CompareSize":  183,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\device.te",
                         "BaseFile":  "basic\\plat_private\\device.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  178,
                         "CompareSize":  189,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\file.te",
                         "BaseFile":  "basic\\plat_private\\file.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  1038,
                         "CompareSize":  1979,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\property_contexts",
                         "BaseFile":  "basic\\plat_private\\property_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  3595,
                         "CompareSize":  3263,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\service_contexts",
                         "BaseFile":  "basic\\plat_private\\service_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  297,
                         "CompareSize":  270,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "BaseFile":  "basic\\plat_private\\surfaceflinger.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  483,
                         "CompareSize":  177,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "BaseFile":  "basic\\plat_private\\system_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  762,
                         "CompareSize":  681,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\device.te",
                         "BaseFile":  "basic\\plat_public\\device.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  149,
                         "CompareSize":  189,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  262,
                         "CompareSize":  165,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "BaseFile":  "bsp\\debug\\non_plat\\untrusted_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  342,
                         "CompareSize":  183,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "bsp\\debug\\plat_private\\system_server.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  299,
                         "CompareSize":  481,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\file.te",
                         "BaseFile":  "bsp\\non_plat\\file.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  3194,
                         "CompareSize":  1979,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "BaseFile":  "bsp\\non_plat\\file_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  13989,
                         "CompareSize":  7603,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\hal_fingerprint_default.te",
                         "BaseFile":  "bsp\\non_plat\\hal_fingerprint_default.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  1275,
                         "CompareSize":  2229,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\hal_graphics_allocator_default.te",
                         "BaseFile":  "bsp\\non_plat\\hal_graphics_allocator_default.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  223,
                         "CompareSize":  184,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\hwservice_contexts",
                         "BaseFile":  "bsp\\non_plat\\hwservice_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  4335,
                         "CompareSize":  1892,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\init.te",
                         "BaseFile":  "bsp\\non_plat\\init.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  1495,
                         "CompareSize":  1367,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\kernel.te",
                         "BaseFile":  "bsp\\non_plat\\kernel.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  390,
                         "CompareSize":  261,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\mtk_hal_c2.te",
                         "BaseFile":  "bsp\\non_plat\\mtk_hal_c2.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  420,
                         "CompareSize":  237,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "BaseFile":  "bsp\\non_plat\\mtk_hal_camera.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  3406,
                         "CompareSize":  1139,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "BaseFile":  "bsp\\non_plat\\priv_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  255,
                         "CompareSize":  344,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\service_contexts",
                         "BaseFile":  "bsp\\non_plat\\service_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  510,
                         "CompareSize":  270,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\vpud_native.te",
                         "BaseFile":  "bsp\\non_plat\\vpud_native.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  422,
                         "CompareSize":  158,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\file.te",
                         "BaseFile":  "bsp\\plat_private\\file.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  2387,
                         "CompareSize":  1979,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "BaseFile":  "bsp\\plat_private\\genfs_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  15148,
                         "CompareSize":  10601,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\property_contexts",
                         "BaseFile":  "bsp\\plat_private\\property_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  8136,
                         "CompareSize":  3263,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "BaseFile":  "bsp\\plat_private\\untrusted_app.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  432,
                         "CompareSize":  183,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "modem\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  713,
                         "CompareSize":  273,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "modem\\property.te",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  713,
                         "CompareSize":  240,
                         "Action":  "Backup and Remove"
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "BaseFile":  "modem\\property_contexts",
                         "Reason":  "Similar sizes - backup before removal",
                         "BaseSize":  809,
                         "CompareSize":  1165,
                         "Action":  "Backup and Remove"
                     }
                 ],
    "ToRemove":  [
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "basic\\debug\\non_plat\\file.te",
                         "Reason":  "Base file is 34.9x larger - removing smaller stub version",
                         "BaseSize":  2371,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
                         "Reason":  "Base file is 5.6x larger - removing smaller stub version",
                         "BaseSize":  1383,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
                         "Reason":  "Base file is 35.5x larger - removing smaller stub version",
                         "BaseSize":  3405,
                         "CompareSize":  96,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
                         "Reason":  "Base file is 6.5x larger - removing smaller stub version",
                         "BaseSize":  1595,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\device.te",
                         "BaseFile":  "basic\\non_plat\\device.te",
                         "Reason":  "Base file is 61.7x larger - removing smaller stub version",
                         "BaseSize":  11668,
                         "CompareSize":  189,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "basic\\non_plat\\file.te",
                         "Reason":  "Base file is 292.6x larger - removing smaller stub version",
                         "BaseSize":  19900,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\file.te",
                         "BaseFile":  "basic\\non_plat\\file.te",
                         "Reason":  "Base file is 10.1x larger - removing smaller stub version",
                         "BaseSize":  19900,
                         "CompareSize":  1979,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "basic\\non_plat\\file_contexts",
                         "Reason":  "Base file is 245.8x larger - removing smaller stub version",
                         "BaseSize":  60474,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "BaseFile":  "basic\\non_plat\\file_contexts",
                         "Reason":  "Base file is 8x larger - removing smaller stub version",
                         "BaseSize":  60474,
                         "CompareSize":  7603,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "BaseFile":  "basic\\non_plat\\genfs_contexts",
                         "Reason":  "Base file is 547.5x larger - removing smaller stub version",
                         "BaseSize":  52559,
                         "CompareSize":  96,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "BaseFile":  "basic\\non_plat\\genfs_contexts",
                         "Reason":  "Base file is 5x larger - removing smaller stub version",
                         "BaseSize":  52559,
                         "CompareSize":  10601,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\hal_audio_default.te",
                         "BaseFile":  "basic\\non_plat\\hal_audio_default.te",
                         "Reason":  "Base file is 4.2x larger - removing smaller stub version",
                         "BaseSize":  7845,
                         "CompareSize":  1853,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\hal_graphics_allocator_default.te",
                         "BaseFile":  "basic\\non_plat\\hal_graphics_allocator_default.te",
                         "Reason":  "Base file is 3.9x larger - removing smaller stub version",
                         "BaseSize":  714,
                         "CompareSize":  184,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\hal_nfc_default.te",
                         "BaseFile":  "basic\\non_plat\\hal_nfc_default.te",
                         "Reason":  "Base file is 3.2x larger - removing smaller stub version",
                         "BaseSize":  146,
                         "CompareSize":  45,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\hwservice.te",
                         "BaseFile":  "basic\\non_plat\\hwservice.te",
                         "Reason":  "Base file is 4x larger - removing smaller stub version",
                         "BaseSize":  1992,
                         "CompareSize":  496,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\init.te",
                         "BaseFile":  "basic\\non_plat\\init.te",
                         "Reason":  "Base file is 53.3x larger - removing smaller stub version",
                         "BaseSize":  4854,
                         "CompareSize":  91,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\init.te",
                         "BaseFile":  "basic\\non_plat\\init.te",
                         "Reason":  "Base file is 3.6x larger - removing smaller stub version",
                         "BaseSize":  4854,
                         "CompareSize":  1367,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\kernel.te",
                         "BaseFile":  "basic\\non_plat\\kernel.te",
                         "Reason":  "Base file is 11x larger - removing smaller stub version",
                         "BaseSize":  2863,
                         "CompareSize":  261,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "BaseFile":  "basic\\non_plat\\kernel.te",
                         "Reason":  "Base file is 29.5x larger - removing smaller stub version",
                         "BaseSize":  2863,
                         "CompareSize":  97,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "BaseFile":  "basic\\non_plat\\mediaserver.te",
                         "Reason":  "Base file is 160.5x larger - removing smaller stub version",
                         "BaseSize":  10111,
                         "CompareSize":  63,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\mtk_hal_c2.te",
                         "BaseFile":  "basic\\non_plat\\mtk_hal_c2.te",
                         "Reason":  "Base file is 11.5x larger - removing smaller stub version",
                         "BaseSize":  2716,
                         "CompareSize":  237,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "BaseFile":  "basic\\non_plat\\mtk_hal_camera.te",
                         "Reason":  "Base file is 12.7x larger - removing smaller stub version",
                         "BaseSize":  14489,
                         "CompareSize":  1139,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "basic\\non_plat\\property.te",
                         "Reason":  "Base file is 47.6x larger - removing smaller stub version",
                         "BaseSize":  12982,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "basic\\non_plat\\property.te",
                         "Reason":  "Base file is 54.1x larger - removing smaller stub version",
                         "BaseSize":  12982,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\property_contexts",
                         "BaseFile":  "basic\\non_plat\\property_contexts",
                         "Reason":  "Base file is 5.9x larger - removing smaller stub version",
                         "BaseSize":  19275,
                         "CompareSize":  3263,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "BaseFile":  "basic\\non_plat\\property_contexts",
                         "Reason":  "Base file is 16.5x larger - removing smaller stub version",
                         "BaseSize":  19275,
                         "CompareSize":  1165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\radio.te",
                         "BaseFile":  "basic\\non_plat\\radio.te",
                         "Reason":  "Base file is 6.9x larger - removing smaller stub version",
                         "BaseSize":  1666,
                         "CompareSize":  241,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "BaseFile":  "basic\\non_plat\\surfaceflinger.te",
                         "Reason":  "Base file is 18.3x larger - removing smaller stub version",
                         "BaseSize":  3243,
                         "CompareSize":  177,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "basic\\non_plat\\system_app.te",
                         "Reason":  "Base file is 8.9x larger - removing smaller stub version",
                         "BaseSize":  1464,
                         "CompareSize":  165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "basic\\non_plat\\system_server.te",
                         "Reason":  "Base file is 17.5x larger - removing smaller stub version",
                         "BaseSize":  8396,
                         "CompareSize":  481,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "BaseFile":  "basic\\non_plat\\vendor_init.te",
                         "Reason":  "Base file is 8.2x larger - removing smaller stub version",
                         "BaseSize":  6516,
                         "CompareSize":  792,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\vpud_native.te",
                         "BaseFile":  "basic\\non_plat\\vpud_native.te",
                         "Reason":  "Base file is 13x larger - removing smaller stub version",
                         "BaseSize":  2052,
                         "CompareSize":  158,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "basic\\plat_private\\file.te",
                         "Reason":  "Base file is 15.3x larger - removing smaller stub version",
                         "BaseSize":  1038,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "basic\\plat_private\\file_contexts",
                         "Reason":  "Base file is 7x larger - removing smaller stub version",
                         "BaseSize":  1728,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "BaseFile":  "basic\\plat_private\\genfs_contexts",
                         "Reason":  "Base file is 13.1x larger - removing smaller stub version",
                         "BaseSize":  1258,
                         "CompareSize":  96,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\init.te",
                         "BaseFile":  "basic\\plat_private\\init.te",
                         "Reason":  "Base file is 6.3x larger - removing smaller stub version",
                         "BaseSize":  569,
                         "CompareSize":  91,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "BaseFile":  "basic\\plat_private\\mediaserver.te",
                         "Reason":  "Base file is 4x larger - removing smaller stub version",
                         "BaseSize":  253,
                         "CompareSize":  63,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "basic\\plat_private\\property.te",
                         "Reason":  "Base file is 14.2x larger - removing smaller stub version",
                         "BaseSize":  3875,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "basic\\plat_private\\property.te",
                         "Reason":  "Base file is 16.1x larger - removing smaller stub version",
                         "BaseSize":  3875,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "BaseFile":  "basic\\plat_private\\property_contexts",
                         "Reason":  "Base file is 3.1x larger - removing smaller stub version",
                         "BaseSize":  3595,
                         "CompareSize":  1165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\radio.te",
                         "BaseFile":  "basic\\plat_private\\radio.te",
                         "Reason":  "Base file is 5.5x larger - removing smaller stub version",
                         "BaseSize":  1328,
                         "CompareSize":  241,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "basic\\plat_private\\system_app.te",
                         "Reason":  "Base file is 4.6x larger - removing smaller stub version",
                         "BaseSize":  762,
                         "CompareSize":  165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "basic\\plat_private\\system_server.te",
                         "Reason":  "Base file is 3.5x larger - removing smaller stub version",
                         "BaseSize":  1682,
                         "CompareSize":  481,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "basic\\plat_public\\file.te",
                         "Reason":  "Base file is 6.3x larger - removing smaller stub version",
                         "BaseSize":  429,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "basic\\plat_public\\property.te",
                         "Reason":  "Base file is 3.3x larger - removing smaller stub version",
                         "BaseSize":  910,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "basic\\plat_public\\property.te",
                         "Reason":  "Base file is 3.8x larger - removing smaller stub version",
                         "BaseSize":  910,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "BaseFile":  "basic\\plat_public\\vtservice.te",
                         "Reason":  "Base file is 5.8x larger - removing smaller stub version",
                         "BaseSize":  300,
                         "CompareSize":  52,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "bsp\\debug\\non_plat\\file.te",
                         "Reason":  "Base file is 5x larger - removing smaller stub version",
                         "BaseSize":  337,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "bsp\\non_plat\\file.te",
                         "Reason":  "Base file is 47x larger - removing smaller stub version",
                         "BaseSize":  3194,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "bsp\\non_plat\\file_contexts",
                         "Reason":  "Base file is 56.9x larger - removing smaller stub version",
                         "BaseSize":  13989,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "BaseFile":  "bsp\\non_plat\\genfs_contexts",
                         "Reason":  "Base file is 30.6x larger - removing smaller stub version",
                         "BaseSize":  2941,
                         "CompareSize":  96,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\hwservice.te",
                         "BaseFile":  "bsp\\non_plat\\hwservice.te",
                         "Reason":  "Base file is 4x larger - removing smaller stub version",
                         "BaseSize":  1962,
                         "CompareSize":  496,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\init.te",
                         "BaseFile":  "bsp\\non_plat\\init.te",
                         "Reason":  "Base file is 16.4x larger - removing smaller stub version",
                         "BaseSize":  1495,
                         "CompareSize":  91,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "BaseFile":  "bsp\\non_plat\\kernel.te",
                         "Reason":  "Base file is 4x larger - removing smaller stub version",
                         "BaseSize":  390,
                         "CompareSize":  97,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "BaseFile":  "bsp\\non_plat\\mediaserver.te",
                         "Reason":  "Base file is 52x larger - removing smaller stub version",
                         "BaseSize":  3277,
                         "CompareSize":  63,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "bsp\\non_plat\\property.te",
                         "Reason":  "Base file is 49.5x larger - removing smaller stub version",
                         "BaseSize":  13515,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "bsp\\non_plat\\property.te",
                         "Reason":  "Base file is 56.3x larger - removing smaller stub version",
                         "BaseSize":  13515,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\property_contexts",
                         "BaseFile":  "bsp\\non_plat\\property_contexts",
                         "Reason":  "Base file is 5.1x larger - removing smaller stub version",
                         "BaseSize":  16569,
                         "CompareSize":  3263,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "BaseFile":  "bsp\\non_plat\\property_contexts",
                         "Reason":  "Base file is 14.2x larger - removing smaller stub version",
                         "BaseSize":  16569,
                         "CompareSize":  1165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\radio.te",
                         "BaseFile":  "bsp\\non_plat\\radio.te",
                         "Reason":  "Base file is 12.3x larger - removing smaller stub version",
                         "BaseSize":  2969,
                         "CompareSize":  241,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "BaseFile":  "bsp\\non_plat\\surfaceflinger.te",
                         "Reason":  "Base file is 19.9x larger - removing smaller stub version",
                         "BaseSize":  3514,
                         "CompareSize":  177,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "bsp\\non_plat\\system_app.te",
                         "Reason":  "Base file is 34.5x larger - removing smaller stub version",
                         "BaseSize":  5687,
                         "CompareSize":  165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "BaseFile":  "bsp\\non_plat\\system_app.te",
                         "Reason":  "Base file is 8.4x larger - removing smaller stub version",
                         "BaseSize":  5687,
                         "CompareSize":  681,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "bsp\\non_plat\\system_server.te",
                         "Reason":  "Base file is 8.8x larger - removing smaller stub version",
                         "BaseSize":  4221,
                         "CompareSize":  481,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "BaseFile":  "bsp\\non_plat\\untrusted_app.te",
                         "Reason":  "Base file is 9.5x larger - removing smaller stub version",
                         "BaseSize":  1735,
                         "CompareSize":  183,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "BaseFile":  "bsp\\non_plat\\vendor_init.te",
                         "Reason":  "Base file is 4.7x larger - removing smaller stub version",
                         "BaseSize":  3713,
                         "CompareSize":  792,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "BaseFile":  "bsp\\non_plat\\vtservice.te",
                         "Reason":  "Base file is 118.3x larger - removing smaller stub version",
                         "BaseSize":  6152,
                         "CompareSize":  52,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "bsp\\plat_private\\file.te",
                         "Reason":  "Base file is 35.1x larger - removing smaller stub version",
                         "BaseSize":  2387,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "bsp\\plat_private\\file_contexts",
                         "Reason":  "Base file is 6.6x larger - removing smaller stub version",
                         "BaseSize":  1633,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "BaseFile":  "bsp\\plat_private\\genfs_contexts",
                         "Reason":  "Base file is 157.8x larger - removing smaller stub version",
                         "BaseSize":  15148,
                         "CompareSize":  96,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\init.te",
                         "BaseFile":  "bsp\\plat_private\\init.te",
                         "Reason":  "Base file is 3.6x larger - removing smaller stub version",
                         "BaseSize":  324,
                         "CompareSize":  91,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "BaseFile":  "bsp\\plat_private\\mediaserver.te",
                         "Reason":  "Base file is 12.1x larger - removing smaller stub version",
                         "BaseSize":  765,
                         "CompareSize":  63,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "BaseFile":  "bsp\\plat_private\\priv_app.te",
                         "Reason":  "Base file is 3.3x larger - removing smaller stub version",
                         "BaseSize":  1135,
                         "CompareSize":  344,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "bsp\\plat_private\\property.te",
                         "Reason":  "Base file is 30.2x larger - removing smaller stub version",
                         "BaseSize":  8236,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "bsp\\plat_private\\property.te",
                         "Reason":  "Base file is 34.3x larger - removing smaller stub version",
                         "BaseSize":  8236,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "BaseFile":  "bsp\\plat_private\\property_contexts",
                         "Reason":  "Base file is 7x larger - removing smaller stub version",
                         "BaseSize":  8136,
                         "CompareSize":  1165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\radio.te",
                         "BaseFile":  "bsp\\plat_private\\radio.te",
                         "Reason":  "Base file is 22.3x larger - removing smaller stub version",
                         "BaseSize":  5370,
                         "CompareSize":  241,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\service_contexts",
                         "BaseFile":  "bsp\\plat_private\\service_contexts",
                         "Reason":  "Base file is 37.8x larger - removing smaller stub version",
                         "BaseSize":  10194,
                         "CompareSize":  270,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "BaseFile":  "bsp\\plat_private\\surfaceflinger.te",
                         "Reason":  "Base file is 8.6x larger - removing smaller stub version",
                         "BaseSize":  1530,
                         "CompareSize":  177,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\system_app.te",
                         "BaseFile":  "bsp\\plat_private\\system_app.te",
                         "Reason":  "Base file is 23.5x larger - removing smaller stub version",
                         "BaseSize":  3881,
                         "CompareSize":  165,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "BaseFile":  "bsp\\plat_private\\system_app.te",
                         "Reason":  "Base file is 5.7x larger - removing smaller stub version",
                         "BaseSize":  3881,
                         "CompareSize":  681,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "BaseFile":  "bsp\\plat_private\\system_server.te",
                         "Reason":  "Base file is 10.6x larger - removing smaller stub version",
                         "BaseSize":  5113,
                         "CompareSize":  481,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "BaseFile":  "bsp\\plat_private\\vtservice.te",
                         "Reason":  "Base file is 14.7x larger - removing smaller stub version",
                         "BaseSize":  763,
                         "CompareSize":  52,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "bsp\\plat_public\\file.te",
                         "Reason":  "Base file is 5x larger - removing smaller stub version",
                         "BaseSize":  342,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\property.te",
                         "BaseFile":  "bsp\\plat_public\\property.te",
                         "Reason":  "Base file is 3.1x larger - removing smaller stub version",
                         "BaseSize":  859,
                         "CompareSize":  273,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "vendor\\property.te",
                         "BaseFile":  "bsp\\plat_public\\property.te",
                         "Reason":  "Base file is 3.6x larger - removing smaller stub version",
                         "BaseSize":  859,
                         "CompareSize":  240,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "public\\file.te",
                         "BaseFile":  "modem\\file.te",
                         "Reason":  "Base file is 3.4x larger - removing smaller stub version",
                         "BaseSize":  230,
                         "CompareSize":  68,
                         "Action":  "Remove"
                     },
                     {
                         "File":  "private\\file_contexts",
                         "BaseFile":  "modem\\file_contexts",
                         "Reason":  "Base file is 7.3x larger - removing smaller stub version",
                         "BaseSize":  1787,
                         "CompareSize":  246,
                         "Action":  "Remove"
                     }
                 ]
}
