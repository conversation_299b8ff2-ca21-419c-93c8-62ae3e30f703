﻿# Camera
NA                                               u:object_r:exported_system_prop:s0
demo.hole                                        u:object_r:exported_system_prop:s0
demo.near                                        u:object_r:exported_system_prop:s0
demo.far                                         u:object_r:exported_system_prop:s0
demo.fb                                          u:object_r:exported_system_prop:s0
oplus.CTP.Tunningmode                            u:object_r:exported_system_prop:s0
oplus.debug.nvram.enable                         u:object_r:exported_system_prop:s0
persist.bps.debug                                u:object_r:exported_system_prop:s0
persist.camera.                                  u:object_r:exported_system_prop:s0
persist.sys.camera.log.                          u:object_r:exported_system_prop:s0
persist.vendor.aps.debug.framenum                u:object_r:exported_system_prop:s0
persist.vendor.arcsoft.sn.dumprawinfo            u:object_r:exported_system_prop:s0
persist.vendor.oplus.rawhdr.dump                 u:object_r:exported_system_prop:s0
ro.camera.res.fmq.size                           u:object_r:exported_system_prop:s0
ro.camera.req.fmq.size                           u:object_r:exported_system_prop:s0
ro.oppo.                                         u:object_r:exported_system_prop:s0
ro.oplus.                                        u:object_r:exported_system_prop:s0
ro.vendor.gfx.32bit.target                       u:object_r:exported_system_prop:s0
ro.vendor.oplus.                                 u:object_r:exported_system_prop:s0
sys.oppo_ftm_mode                                u:object_r:exported_system_prop:s0
vendor.aps.                                      u:object_r:exported_system_prop:s0
vendor.arcsoft.                                  u:object_r:exported_system_prop:s0
vendor.debug.is.rioapk                           u:object_r:exported_system_prop:s0
vendor.debug.magnet.enable                       u:object_r:exported_system_prop:s0
vendor.oplus.                                    u:object_r:exported_system_prop:s0
vendor.oppo.                                     u:object_r:exported_system_prop:s0
vendor.property.debug.caliexif.enable            u:object_r:exported_system_prop:s0
# Fingerprint
oplus.fingerprint.                               u:object_r:system_fingerprint_prop:s0
gf.debug.                                        u:object_r:system_fingerprint_prop:s0
oplus.optical.                                   u:object_r:system_fingerprint_prop:s0
# Oplus
sys.oplus.mm.                                    u:object_r:system_oplus_audio_prop:s0
audio.tuning.def_path                            u:object_r:system_oplus_audio_prop:s0
# Version
ro.separate.soft                                 u:object_r:system_oplus_project_prop:s0
ro.oplus.version.                                u:object_r:system_oplus_project_prop:s0
ro.oplus.image.                                  u:object_r:system_oplus_project_prop:s0
# Realme Parts
persist.cabc_profile                             u:object_r:system_cabc_prop:s0
persist.vib_strength                             u:object_r:system_vib_strength_prop:s0
#Rising Specific
ro.rising.version				 u:object_r:build_prop:s0
