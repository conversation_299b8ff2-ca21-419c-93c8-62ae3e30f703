<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project
     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
          http://www.apache.org/licenses/LICENSE-2.0
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- These resources are around just to allow their values to be customizedfor different hardware and product builds.  Do not translate. -->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">    

    <!-- Power Management: Specifies whether to decouple the auto-suspend state of the
         device from the display on/off state.
         When false, autosuspend_disable() will be called before the display is turned on
         and autosuspend_enable() will be called after the display is turned off.
         This mode provides best compatibility for devices using legacy power management
         features such as early suspend / late resume.
         When true, autosuspend_display() and autosuspend_enable() will be called
         independently of whether the display is being turned on or off.  This mode
         enables the power manager to suspend the application processor while the
         display is on.
         This resource should be set to "true" when a doze component has been specified
         to maximize power savings but not all devices support it.
         Refer to autosuspend.h for details.
    -->
    <bool name="config_powerDecoupleAutoSuspendModeFromDisplay">true</bool>


    <!-- Power Management: Specifies whether to decouple the interactive state of the
         device from the display on/off state.
         When false, setInteractive(..., true) will be called before the display is turned on
         and setInteractive(..., false) will be called after the display is turned off.
         This mode provides best compatibility for devices that expect the interactive
         state to be tied to the display state.
         When true, setInteractive(...) will be called independently of whether the display
         is being turned on or off.  This mode enables the power manager to reduce
         clocks and disable the touch controller while the display is on.
         This resource should be set to "true" when a doze component has been specified
         to maximize power savings but not all devices support it.
         Refer to power.h for details.
    -->
    <bool name="config_powerDecoupleInteractiveModeFromDisplay">true</bool>

    <!-- Boolean indicating whether the HWC setColorTransform function can be performed efficiently in hardware. -->
    <bool name="config_setColorTransformAccelerated">true</bool>

    <!-- Whether or not wcg (wide color gamut) should be enabled on this device,
         we only enabled it while the device has ability of mixed color spaces composition -->
    <bool name="config_enableWcgMode">true</bool>

    <!-- List supported color modes. -->
    <integer-array name="config_availableColorModes">
        <item>0</item> <!-- COLOR_MODE_NATURAL -->
        <item>1</item> <!-- COLOR_MODE_BOOSTED -->
        <item>3</item> <!-- COLOR_MODE_AUTOMATIC -->
    </integer-array>

    <!-- Color mode to use when accessibility transforms are enabled. This color mode must be
     supported by the device, but not necessarily appear in config_availableColorModes. The
     regularly selected color mode will be used if this value is negative. -->
    <integer name="config_accessibilityColorMode">2</integer>

    <!-- The following two arrays specify which color space to use for display composition when a
         certain color mode is active.
         Composition color spaces are defined in android.view.Display.COLOR_MODE_xxx, and color
         modes are defined in ColorDisplayManager.COLOR_MODE_xxx and
         ColorDisplayManager.VENDOR_COLOR_MODE_xxx.
         The color space COLOR_MODE_DEFAULT (0) lets the system select the most appropriate
         composition color space for currently displayed content. Other values (e.g.,
         COLOR_MODE_SRGB) override system selection; these other color spaces must be supported by
         the device for for display composition.
         If a color mode does not have a corresponding color space specified in this array, the
         currently set composition color space will not be modified.-->
    <integer-array name="config_displayCompositionColorModes">
        <item>0</item> <!-- COLOR_MODE_NATURAL   -->
        <item>1</item> <!-- COLOR_MODE_BOOSTED   -->
        <item>2</item> <!-- COLOR_MODE_SATURATED -->
        <item>3</item> <!-- COLOR_MODE_AUTOMATIC -->
    </integer-array>
    <integer-array name="config_displayCompositionColorSpaces">
        <item>0</item> <!-- COLOR_MODE_DEFAULT    -->
        <item>0</item> <!-- COLOR_MODE_DEFAULT    -->
        <item>0</item> <!-- COLOR_MODE_DEFAULT    -->
        <item>9</item> <!-- COLOR_MODE_DISPLAY_P3 -->
    </integer-array>

    <!-- Whether devices suports in-display fingerprint when screen is off -->
    <bool name="config_supportsScreenOffUdfps">true</bool>

    <!-- Boost Framework -->
    <bool name="config_supportsBoostFramework">true</bool>

    <!-- Whether to enable Lineage Health Service -->
    <bool name="config_lineageHealthSupported">true</bool>


    <!-- Enable face auth only when swiping security view -->
    <bool name="config_faceAuthOnlyOnSecurityView">true</bool>

</resources>
