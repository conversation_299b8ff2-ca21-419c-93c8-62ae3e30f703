# Merge SELinux Policy Duplicates
# Strategy: Merge device-specific policies from sepolicy into sepolicy_vndr structure
# while preserving both MediaTek vendor policies and device-specific customizations

param(
    [switch]$DryRun = $true,
    [switch]$Force = $false
)

Write-Host "SELinux Policy Duplicate Merger" -ForegroundColor Green
Write-Host "Dry Run Mode: $DryRun" -ForegroundColor Yellow
Write-Host ""

Write-Host "=== STRATEGY ===" -ForegroundColor Magenta
Write-Host "1. sepolicy_vndr contains comprehensive MediaTek vendor policies (readonly)" -ForegroundColor White
Write-Host "2. sepolicy contains device-specific policies that complement vendor policies" -ForegroundColor White
Write-Host "3. Instead of removing duplicates, we'll create a merged structure" -ForegroundColor White
Write-Host "4. Device-specific policies will be preserved in a separate structure" -ForegroundColor White
Write-Host ""

# Create a mapping of key files that need special handling
$criticalFiles = @{
    "file_contexts" = @{
        Description = "File context mappings"
        Strategy = "Merge device-specific contexts with vendor contexts"
    }
    "property_contexts" = @{
        Description = "Property context mappings"
        Strategy = "Merge device-specific properties with vendor properties"
    }
    "genfs_contexts" = @{
        Description = "Generic filesystem contexts"
        Strategy = "Merge device-specific genfs with vendor genfs"
    }
    "hwservice_contexts" = @{
        Description = "Hardware service contexts"
        Strategy = "Merge device-specific hwservices with vendor hwservices"
    }
    "service_contexts" = @{
        Description = "Service contexts"
        Strategy = "Merge device-specific services with vendor services"
    }
}

# Analyze current structure
Write-Host "=== CURRENT STRUCTURE ANALYSIS ===" -ForegroundColor Cyan

$sepolicyFiles = Get-ChildItem -Path "sepolicy" -Recurse -File | ForEach-Object {
    [PSCustomObject]@{
        RelativePath = $_.FullName.Substring((Resolve-Path "sepolicy").Path.Length + 1)
        FullPath = $_.FullName
        Name = $_.Name
        Size = $_.Length
        Directory = $_.DirectoryName.Substring((Resolve-Path "sepolicy").Path.Length + 1)
    }
}

$sepolicyVndrFiles = Get-ChildItem -Path "sepolicy_vndr" -Recurse -File | ForEach-Object {
    [PSCustomObject]@{
        RelativePath = $_.FullName.Substring((Resolve-Path "sepolicy_vndr").Path.Length + 1)
        FullPath = $_.FullName
        Name = $_.Name
        Size = $_.Length
        Directory = $_.DirectoryName.Substring((Resolve-Path "sepolicy_vndr").Path.Length + 1)
    }
}

Write-Host "sepolicy files: $($sepolicyFiles.Count)" -ForegroundColor Green
Write-Host "sepolicy_vndr files: $($sepolicyVndrFiles.Count)" -ForegroundColor Green
Write-Host ""

# Find actual duplicates by name
$duplicatesByName = @{}
foreach ($sepFile in $sepolicyFiles) {
    $matchingVndrFiles = $sepolicyVndrFiles | Where-Object { $_.Name -eq $sepFile.Name }
    if ($matchingVndrFiles.Count -gt 0) {
        if (-not $duplicatesByName.ContainsKey($sepFile.Name)) {
            $duplicatesByName[$sepFile.Name] = @{
                SepolicyFiles = @()
                VndrFiles = @()
            }
        }
        $duplicatesByName[$sepFile.Name].SepolicyFiles += $sepFile
        $duplicatesByName[$sepFile.Name].VndrFiles += $matchingVndrFiles
    }
}

Write-Host "=== DUPLICATE FILES BY NAME ===" -ForegroundColor Red
Write-Host "Found $($duplicatesByName.Keys.Count) duplicate file names" -ForegroundColor Red
Write-Host ""

# Analyze each duplicate
$mergeActions = @()
foreach ($fileName in $duplicatesByName.Keys) {
    $duplicate = $duplicatesByName[$fileName]
    
    Write-Host "File: $fileName" -ForegroundColor Yellow
    Write-Host "  sepolicy instances: $($duplicate.SepolicyFiles.Count)" -ForegroundColor Gray
    Write-Host "  sepolicy_vndr instances: $($duplicate.VndrFiles.Count)" -ForegroundColor Gray
    
    # Determine action based on file type
    $action = "preserve_both"
    $reason = "Different purposes - preserve both"
    
    if ($criticalFiles.ContainsKey($fileName)) {
        $action = "merge_content"
        $reason = $criticalFiles[$fileName].Strategy
    } elseif ($fileName.EndsWith(".te")) {
        $action = "preserve_both"
        $reason = "Policy files may have different scopes - preserve both"
    }
    
    $mergeActions += [PSCustomObject]@{
        FileName = $fileName
        Action = $action
        Reason = $reason
        SepolicyFiles = $duplicate.SepolicyFiles
        VndrFiles = $duplicate.VndrFiles
    }
    
    Write-Host "  Action: $action" -ForegroundColor $(if($action -eq "merge_content") {"DarkYellow"} else {"Green"})
    Write-Host "  Reason: $reason" -ForegroundColor Gray
    Write-Host ""
}

# Create reorganization plan
Write-Host "=== REORGANIZATION PLAN ===" -ForegroundColor Magenta
Write-Host "1. Keep sepolicy_vndr as-is (readonly MediaTek vendor policies)" -ForegroundColor White
Write-Host "2. Reorganize sepolicy into device-specific structure" -ForegroundColor White
Write-Host "3. Create clear separation between vendor and device policies" -ForegroundColor White
Write-Host "4. Preserve all unique content from both sources" -ForegroundColor White
Write-Host ""

# Proposed new structure
$newStructure = @"
sepolicy_vndr/          # MediaTek vendor policies (readonly)
├── basic/
├── bsp/
├── legacy/
└── modem/

sepolicy/               # Device-specific policies
├── device/             # Device-specific overrides/additions
│   ├── private/        # Device private policies
│   ├── public/         # Device public policies
│   └── vendor/         # Device vendor customizations
└── original/           # Backup of original sepolicy structure
    ├── private/
    ├── public/
    └── vendor/
"@

Write-Host "Proposed structure:" -ForegroundColor Green
Write-Host $newStructure -ForegroundColor Gray
Write-Host ""

# Summary
Write-Host "=== SUMMARY ===" -ForegroundColor Magenta
Write-Host "Total duplicate file names: $($duplicatesByName.Keys.Count)" -ForegroundColor Red
Write-Host "Files to preserve both: $($mergeActions | Where-Object {$_.Action -eq 'preserve_both'} | Measure-Object).Count" -ForegroundColor Green
Write-Host "Files to merge content: $($mergeActions | Where-Object {$_.Action -eq 'merge_content'} | Measure-Object).Count" -ForegroundColor DarkYellow
Write-Host ""

if (-not $DryRun) {
    Write-Host "=== EXECUTING REORGANIZATION ===" -ForegroundColor Red
    Write-Host "This would reorganize the sepolicy structure..." -ForegroundColor Yellow
    Write-Host "Implementation pending user confirmation..." -ForegroundColor Yellow
} else {
    Write-Host "DRY RUN: No changes made" -ForegroundColor DarkYellow
    Write-Host "This analysis shows that the 'duplicates' are actually complementary policies" -ForegroundColor Green
    Write-Host "Recommendation: Keep both structures but organize them clearly" -ForegroundColor Green
}

# Save analysis
$analysis = @{
    DuplicatesByName = $duplicatesByName
    MergeActions = $mergeActions
    Summary = @{
        TotalDuplicateNames = $duplicatesByName.Keys.Count
        PreserveBoth = ($mergeActions | Where-Object {$_.Action -eq 'preserve_both'} | Measure-Object).Count
        MergeContent = ($mergeActions | Where-Object {$_.Action -eq 'merge_content'} | Measure-Object).Count
    }
}

$analysisFile = "sepolicy_merge_analysis.json"
$analysis | ConvertTo-Json -Depth 5 | Out-File -FilePath $analysisFile -Encoding UTF8
Write-Host "Analysis saved to: $analysisFile" -ForegroundColor Green

return $analysis
