# ==============================================
# Common SEPolicy Rule
# ==============================================

# Access devices.
allow system_server touch_device:chr_file rw_file_perms;
allow system_server stpant_device:chr_file rw_file_perms;
allow system_server devmap_device:chr_file r_file_perms;
allow system_server irtx_device:chr_file rw_file_perms;
allow system_server qemu_pipe_device:chr_file rw_file_perms;
allow system_server wmtWifi_device:chr_file w_file_perms;

# Add for proc_btdbg
allow system_server proc_btdbg:file rw_file_perms;
# Add for bootprof
allow system_server proc_bootprof:file rw_file_perms;

# /data/core access.
allow system_server aee_core_data_file:dir r_dir_perms;

# Perform Binder IPC.
allow system_server zygote:binder impersonate;

# For dumpsys.
allow system_server aee_dumpsys_data_file:file w_file_perms;

# Querying zygote socket.
allow system_server zygote:unix_stream_socket { getopt getattr };

# Allow system_server to read/write /sys/power/dcm_state
allow system_server sysfs_dcm:file rw_file_perms;

# Data : WK16.42
# Operator: Whitney bring up
# Purpose: call surfaceflinger due to powervr
allow system_server surfaceflinger:fifo_file rw_file_perms;

# Date : W16.42
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow system_server gpu_device:dir search;

# Date : W16.43
# Operation : Integration
# Purpose : DRM / DRI GPU driver required
allow system_server sw_sync_device:chr_file rw_file_perms;

# Date : WK16.44
# Purpose: Allow to access UART1 ttyMT1
allow system_server ttyMT_device:chr_file rw_file_perms;

# Date : WK17.52
# Purpose: Allow to access UART1 ttyS
allow system_server ttyS_device:chr_file rw_file_perms;

# Date:W16.46
# Operation : thermal hal Feature developing
# Purpose : thermal hal interface permission
allow system_server proc_mtktz:dir search;
allow system_server proc_mtktz:file r_file_perms;

# Date:W17.02
# Operation : audio hal developing
# Purpose : audio hal interface permission
allow system_server hal_audio_default:process { getsched setsched };

# Dat: 2017/02/14
# Purpose: allow get telephony Sensitive property
get_prop(system_server, vendor_mtk_telephony_sensitive_prop)

# Date:W17.07
# Operation : bt hal
# Purpose : bt hal interface permission
binder_call(system_server, mtk_hal_bluetooth)

# Operation : light hal developing
# Purpose : light hal interface permission
binder_call(system_server, mtk_hal_light)

# Date:W17.21
# Operation : gnss hal
# Purpose : gnss hal interface permission
hal_client_domain(system_server, hal_gnss)

# Date:W17.26
# Operation : imsa hal
# Purpose : imsa hal interface permission
binder_call(system_server, mtk_hal_imsa)

# Date:W17.28
# Operation : camera hal developing
# Purpose : camera hal binder_call permission
binder_call(system_server, mtk_hal_camera)

# Date:W17.31
# Operation : mpe sensor hidl developing
# Purpose : mpe sensor hidl permission
binder_call(system_server, mnld)

# Date : WK17.32
# Operation : Migration
# Purpose : for network log dumpsys setting/netd information
#           audit(0.0:914): avc: denied { write } for path="pipe:[46088]"
#           dev="pipefs" ino=46088 scontext=u:r:system_server:s0
#           tcontext=u:r:netdiag:s0 tclass=fifo_file permissive=1
allow system_server netdiag:fifo_file w_file_perms;

# Date : WK17.32
# Operation : Migration
# Purpose : for DHCP Client ip recover functionality
allow system_server dhcp_data_file:dir rw_dir_perms;
allow system_server dhcp_data_file:file create_file_perms;

# Date:W17.35
# Operation : lbs hal
# Purpose : lbs hidl interface permission
hal_client_domain(system_server, hal_mtk_lbs)

# Date : WK17.12
# Operation : MT6799 SQC
# Purpose : Change thermal config
get_prop(system_server, vendor_mtk_thermal_config_prop)

# Date : WK17.43
# Operation : Migration
# Purpose : perfmgr permission
allow system_server proc_perfmgr:dir r_dir_perms;
allow system_server proc_perfmgr:file r_file_perms;
allowxperm system_server proc_perfmgr:file ioctl {
  PERFMGR_FPSGO_QUEUE
  PERFMGR_FPSGO_DEQUEUE
  PERFMGR_FPSGO_QUEUE_CONNECT
  PERFMGR_FPSGO_BQID
  PERFMGR_FPSGO_SWAP_BUFFER
};

# Date : W18.22
# Operation : MTK wifi hal migration
# Purpose : MTK wifi hal interface permission
binder_call(system_server, mtk_hal_wifi)

# Date : WK19.7
# Operation: Q migration
# Purpose : Allow system_server to use ioctl/ioctlcmd
allow system_server proc_ged:file rw_file_perms;
allowxperm system_server proc_ged:file ioctl { proc_ged_ioctls };

# Date: 2019/06/14
# Operation : when WFD turnning on, turn off hdmi
hal_client_domain(system_server, hal_mtk_hdmi)

# Date:2019/10/09
# Operation:Q Migration
allow system_server proc_cmdq_debug:file getattr;
allow system_server proc_last_kmsg:file r_file_perms;
allow system_server proc_cm_mgr:dir search;
allow system_server proc_isp_p2:dir search;
allow system_server proc_thermal:dir search;
allow system_server proc_atf_log:dir search;
allow system_server proc_cpufreq:dir search;
allow system_server proc_mtkcooler:dir search;
allow system_server proc_ppm:dir search;

# Date : 2019/10/11
# Operation : Q Migration
allow system_server proc_wlan_status:file getattr;

# Date : 2019/10/11
# Operation : Q Migration
allow system_server sysfs_pages_shared:file r_file_perms;
allow system_server sysfs_pages_sharing:file r_file_perms;
allow system_server sysfs_pages_unshared:file r_file_perms;
allow system_server sysfs_pages_volatile:file r_file_perms;

# Date:2019/10/14
# Operation: Q Migration
# Purpose : power_hal_mgr_service may use libmtkperf_client
allow system_server sysfs_boot_mode:file r_file_perms;

# Date : 2019/10/22
# Operation : Q Migration
allow system_server self:capability sys_module;

# Date : 2019/10/22
# Operation : Q Migration
dontaudit system_server sdcardfs:file r_file_perms;

# Date : 2019/10/26
# Operation : Q Migration
allow system_server mtk_hal_camera:process sigkill;
allow system_server kernel:system syslog_read;

# Date : 2019/10/30
# Operation : Q Migration
allow system_server proc_chip:dir search;
allow system_server zygote:process setsched;

# Date : 2019/11/21
# Operation : Q Migration
allow system_server sf_rtt_file:dir rmdir;

# Date : 2019/11/29
# Operation : Q Migration
allow system_server storage_stub_file:dir getattr;

# Date : 2020/05/12
# Operation : R Migration
allow system_server proc_ppm:file r_file_perms;

# Date: 2019/11/12
# Purpose: Allow system server to access mtk jpeg
allow system_server proc_mtk_jpeg:file rw_file_perms;
allowxperm system_server proc_mtk_jpeg:file ioctl {
      JPG_BRIDGE_DEC_IO_LOCK
      JPG_BRIDGE_DEC_IO_WAIT
      JPG_BRIDGE_DEC_IO_UNLOCK
};

# Date : 2020/06/30
# Operation : R Migration
dontaudit system_server kernel:process sigkill;

# Date : 2021/04/24
# Operation: addwindow
# Purpose: Get the variable value of touch report rate
get_prop(system_server, vendor_mtk_input_report_rate_prop)


# Date : 2021/07/20
# Operation : S Migration
# Purpose : dontaudit system_server is not allowed to getopt 'shell' type of unix_stream_socket
dontaudit system_server shell:unix_stream_socket getopt;

# Search /proc/cpuidle
allow system_server proc_cpuidle:dir search;

# Date:2021/08/23
# Operation:allow CachedAppOptimi to getattr from /proc/mtk_mdp_debug
allow system_server proc_mtk_mdp_debug:file getattr;

# Search /proc/mtk_mdp_debug
allow system_server proc_mtk_mdp_debug:dir search;

# For AudioManager/WiredAccessoryManager, for Extcon uevent
# listeners
allow system_server sysfs_extcon:file r_file_perms;

# When ams cleans up the process, it should not kill mtk_hal_bluetooth
dontaudit system_server mtk_hal_bluetooth:process sigkill;

# Write tempfs for CachedAppOptimi
allow system_server tmpfs:file w_file_perms;

# Write mediaserver_tmpfs for CachedAppOptimi
allow system_server mediaserver_tmpfs:file w_file_perms;
# Purpose : dontaudit system_server is not allowed to kill eara_io
dontaudit system_server hal_wifi_default:process sigkill;
dontaudit system_server eara_io:process sigkill;

# Purpose : dontaudit system_server is not allowed to kill hal_audio_default
dontaudit system_server hal_audio_default:process sigkill;
dontaudit system_server mtk_hal_c2:process sigkill;

# Search /proc/mgq
allow system_server proc_mgq:dir search;

# when anr dump process, SystemServer need send sigal
allow system_server mtk_hal_pq:process signal;

hal_client_domain(system_server, hal_mtkcodecservice)
