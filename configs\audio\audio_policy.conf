#
# Audio policy configuration for generic device builds (goldfish audio HAL - emulator)
#

# Global configuration section: lists input and output devices always present on the device
# as well as the output device selected by default.
# Devices are designated by a string that corresponds to the enum in audio.h

#global_configuration {
#  attached_output_devices AUDIO_DEVICE_OUT_SPEAKER|AUDIO_DEVICE_OUT_EARPIECE
#  default_output_device AUDIO_DEVICE_OUT_SPEAKER
#  attached_input_devices AUDIO_DEVICE_IN_BUILTIN_MIC|AUDIO_DEVICE_IN_REMOTE_SUBMIX|AUDIO_DEVICE_IN_FM_TUNER
#}

# audio hardware module section: contains descriptors for all audio hw modules present on the
# device. Each hw module node is named after the corresponding hw module library base name.
# For instance, "primary" corresponds to audio.primary.<device>.so.
# The "primary" module is mandatory and must include at least one output with
# AUDIO_OUTPUT_FLAG_PRIMARY flag.
# Each module descriptor contains one or more output profile descriptors and zero or more
# input profile descriptors. Each profile lists all the parameters supported by a given output
# or input stream category.
# The "channel_masks", "formats", "devices" and "flags" are specified using strings corresponding
# to enums in audio.h and audio_policy.h. They are concatenated by use of "|" without space or "\n".

audio_hw_modules {
  primary {
  global_configuration {
      attached_output_devices AUDIO_DEVICE_OUT_SPEAKER|AUDIO_DEVICE_OUT_EARPIECE
      default_output_device AUDIO_DEVICE_OUT_SPEAKER
      attached_input_devices AUDIO_DEVICE_IN_BUILTIN_MIC|AUDIO_DEVICE_IN_FM_TUNER|AUDIO_DEVICE_IN_VOICE_CALL
      audio_hal_version 3.0
    }
  devices {
      headset {
        type AUDIO_DEVICE_OUT_WIRED_HEADSET
        gains {
          gain_1 {
            mode AUDIO_GAIN_MODE_JOINT
            channel_mask AUDIO_CHANNEL_OUT_STEREO
            min_value_mB -6400
            max_value_mB 0
            default_value_mB 0
            step_value_mB 100
            min_ramp_ms 0
            max_ramp_ms 0
          }
        }
      }
      headphone {
        type AUDIO_DEVICE_OUT_WIRED_HEADPHONE
        gains {
          gain_1 {
            mode AUDIO_GAIN_MODE_JOINT
            channel_mask AUDIO_CHANNEL_OUT_STEREO
            min_value_mB -6400
            max_value_mB 0
            default_value_mB 0
            step_value_mB 100
            min_ramp_ms 0
            max_ramp_ms 0
          }
        }
      }
    }
    outputs {
      primary {
        sampling_rates 44100|48000|96000|192000
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_32_BIT|AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_EARPIECE|AUDIO_DEVICE_OUT_SPEAKER|AUDIO_DEVICE_OUT_WIRED_HEADSET|AUDIO_DEVICE_OUT_WIRED_HEADPHONE|AUDIO_DEVICE_OUT_ANLG_DOCK_HEADSET|AUDIO_DEVICE_OUT_DGTL_DOCK_HEADSET|AUDIO_DEVICE_OUT_ALL_SCO|AUDIO_DEVICE_OUT_FM|AUDIO_DEVICE_OUT_DEFAULT
        flags AUDIO_OUTPUT_FLAG_PRIMARY
        gains {
          gain_1 {
            mode AUDIO_GAIN_MODE_JOINT
            channel_mask AUDIO_CHANNEL_OUT_STEREO
            min_value_mB -6400
            max_value_mB 0
            default_value_mB 0
            step_value_mB 100
            min_ramp_ms 0
            max_ramp_ms 0
          }
        }
      }
      hdmi_stereo {
        sampling_rates 44100
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_32_BIT|AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_AUX_DIGITAL
      }
      hdmi_multi_channel {
        sampling_rates 32000|44100|48000
        channel_masks AUDIO_CHANNEL_OUT_5POINT1|AUDIO_CHANNEL_OUT_7POINT1
        formats AUDIO_FORMAT_PCM_32_BIT|AUDIO_FORMAT_PCM_8_24_BIT|AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_AUX_DIGITAL
        flags AUDIO_OUTPUT_FLAG_DIRECT
      }
      fast {
        sampling_rates 44100|48000|96000|192000
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_32_BIT|AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_SPEAKER|AUDIO_DEVICE_OUT_WIRED_HEADSET|AUDIO_DEVICE_OUT_WIRED_HEADPHONE|AUDIO_DEVICE_OUT_EARPIECE
        flags AUDIO_OUTPUT_FLAG_FAST
      }
    }
    inputs {
      primary {
        sampling_rates 8000|16000|32000|44100|48000
        channel_masks AUDIO_CHANNEL_IN_MONO|AUDIO_CHANNEL_IN_STEREO|AUDIO_CHANNEL_IN_VOICE_UPLINK|AUDIO_CHANNEL_IN_VOICE_DNLINK
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_IN_COMMUNICATION|AUDIO_DEVICE_IN_BUILTIN_MIC|AUDIO_DEVICE_IN_AMBIENT|AUDIO_DEVICE_IN_WIRED_HEADSET|AUDIO_DEVICE_IN_AUX_DIGITAL|AUDIO_DEVICE_IN_MATV|AUDIO_DEVICE_IN_VOICE_CALL|AUDIO_DEVICE_IN_BACK_MIC|AUDIO_DEVICE_IN_ALL_SCO|AUDIO_DEVICE_IN_FM_TUNER|AUDIO_DEVICE_IN_DEFAULT
      }
    }
  }
  a2dp {
  global_configuration {
      audio_hal_version 2.0
    }
    outputs {
      a2dp {
        sampling_rates 44100
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_ALL_A2DP
      }
    }
  inputs {
      a2dp {
        sampling_rates 44100
        channel_masks AUDIO_CHANNEL_IN_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_IN_BLUETOOTH_A2DP
      }
    }
  }
  usb {
  global_configuration {
      audio_hal_version 2.0
    }
    outputs {
      usb_accessory {
        sampling_rates 44100
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_USB_ACCESSORY
      }
      usb_device {
        sampling_rates dynamic
        channel_masks dynamic
        formats dynamic
        devices AUDIO_DEVICE_OUT_USB_DEVICE
      }
    }
    inputs {
      usb_device {
        sampling_rates dynamic
        channel_masks AUDIO_CHANNEL_IN_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_IN_USB_DEVICE
      }
    }
  }
  r_submix {
  global_configuration {
      attached_input_devices AUDIO_DEVICE_IN_REMOTE_SUBMIX
      audio_hal_version 2.0
    }
    outputs {
      r_submix {
        sampling_rates 48000
        channel_masks AUDIO_CHANNEL_OUT_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_OUT_REMOTE_SUBMIX
      }
    }
    inputs {
      r_submix {
        sampling_rates 48000
        channel_masks AUDIO_CHANNEL_IN_STEREO
        formats AUDIO_FORMAT_PCM_16_BIT
        devices AUDIO_DEVICE_IN_REMOTE_SUBMIX
      }
    }
  }
}
