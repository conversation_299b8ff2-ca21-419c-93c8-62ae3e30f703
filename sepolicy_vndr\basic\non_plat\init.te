# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.34
# Operation : Migration
# Purpose : for L early bring up: add for nvram command in init rc files
allow init nvram_data_file:dir create_dir_perms;
allow init nvram_data_file:lnk_file r_file_perms;
allow init nvdata_file:lnk_file r_file_perms;
allow init nvdata_file:dir { create_dir_perms mounton };

#============= init ==============
# Date : W14.42
# Operation : Migration
# Purpose : for L : add for partition (chown/chmod)
allow init system_block_device:blk_file setattr;
allow init nvram_device:blk_file setattr;
allow init seccfg_block_device:blk_file setattr;
allow init secro_block_device:blk_file setattr;
allow init frp_block_device:blk_file setattr;
allow init logo_block_device:blk_file setattr;
allow init para_block_device:blk_file { setattr w_file_perms };
allow init recovery_block_device:blk_file setattr;

# Date : WK15.30
# Operation : Migration
# Purpose : format wiped partition with "formattable" and "check" flag in fstab file
allow init protect1_block_device:blk_file rw_file_perms;
allow init protect2_block_device:blk_file rw_file_perms;
allow init userdata_block_device:blk_file rw_file_perms;
allow init cache_block_device:blk_file rw_file_perms;
allow init nvdata_device:blk_file w_file_perms;
allow init persist_block_device:blk_file rw_file_perms;
allow init nvcfg_block_device:blk_file rw_file_perms;
allow init odm_block_device:blk_file rw_file_perms;
allow init oem_block_device:blk_file rw_file_perms;

# Date : W16.28
# Operation : Migration
# Purpose : enable modules capability
allow init self:capability sys_module;
allow init kernel:system module_request;

# Date : WK16.35
# Operation : Migration
# Purpose : create symbolic link from /mnt/sdcard to /sdcard
allow init tmpfs:lnk_file create_file_perms;

# Date:W17.07
# Operation : bt hal
# Purpose : bt hal interface permission
allow init mtk_hal_bluetooth_exec:file getattr;

# Date : W17.20
# Purpose: Enable PRODUCT_FULL_TREBLE
allow init vendor_block_device:lnk_file relabelto;

# Date : WK17.21
# Purpose: Fix gnss hal service fail
allow init mtk_hal_gnss_exec:file getattr;

# Fix boot up violation
allow init debugfs_tracing_instances:file relabelfrom;

# Date: W17.22
# Operation : New Feature
# Purpose : Add for A/B system
allow init oemfs:dir mounton;
allow init protect_f_data_file:dir mounton;
allow init protect_s_data_file:dir mounton;
allow init nvcfg_file:dir mounton;
allow init mcf_ota_file:dir mounton;
allow init persist_data_file:dir mounton;

# Date : WK17.39
# Operation : able to relabel mntl block device link
# Purpose : Correct permission for mntl
allow init expdb_block_device:lnk_file relabelto;
allow init mcupmfw_block_device:lnk_file relabelto;
allow init tee_block_device:lnk_file relabelto;

# Date : WK17.43
# Operation : able to insert fpsgo kernel module
# Purpose : Correct permission for fpsgo
allow init rootfs:system module_load;

# Date: W17.43
# Operation : module load
# Purpose : insmod LKM under /vendor (connsys module KO)
allow init vendor_file:system module_load;

# Date : WK17.46
# Operation : feature porting
# Purpose : kernel module verification
allow init kernel:key search;

# Date : WK17.50
# Operation : boost cpu while booting
# Purpose : enhance boottime
allow init proc_perfmgr:file w_file_perms;
allow init proc_wmtdbg:file w_file_perms;

# Date : W18.20
# Operation : mount soc vendor's partition when booting
allow init mnt_vendor_file:dir mounton;

# Date : W19.28
# Purpose: Allow to setattr /proc/last_kmsg
allow init proc_last_kmsg:file setattr;

# Purpose: Allow to write /proc/cpu/alignment
allow init proc_cpu_alignment:file w_file_perms;

# Purpose: Allow to relabelto for selinux_android_restorecon
allow init boot_block_device:lnk_file relabelto;
allow init vbmeta_block_device:lnk_file relabelto;

# Purpose: Allow to write /proc/mtprintk
allow init proc_mtprintk:file w_file_perms;

# Date : 2020/08/05
# Purpose: Allow to write /proc/driver/wmt_user_proc
allow init proc_wmtuserproc:file w_file_perms;

# Date: 2020/09/02
# Operation: R migration
# Purpose: Add permission for pl path utilities to add symlink to raw pl
recovery_only(`
  domain_trans(init, rootfs, update_engine)
')

# Date : 2020/12/23
# Purpose: Allow init to write /proc/driver/conninfra_dbg
allow init proc_conninfradbg:file w_file_perms;
# Date : 2021/07/15
# Purpose: Add permission for pl path utilities
domain_auto_trans(init, postinstall_file, update_engine)

# Date : 2021/09/13
# Purpose: Add permission for mtk_core_ctl
allow init sysfs_mtk_core_ctl:dir r_dir_perms;
allow init sysfs_mtk_core_ctl:file rw_file_perms;

allow init xcap_socket:sock_file create_file_perms;

# Allow init to write to sysfs_devices_block
allow init sysfs_devices_block:file w_file_perms;
