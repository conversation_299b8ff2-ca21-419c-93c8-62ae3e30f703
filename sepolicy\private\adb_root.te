# ADB and Root Management Policy
# This file contains SELinux policies for ADB, Magisk, LSPosed, KernelSU and other root management tools

# Allow init to access ADB data files for root management
# This is needed for Magisk modules, LSPosed, KernelSU, etc.
allow init adb_data_file:dir { create_dir_perms search read open getattr };
allow init adb_data_file:file { create_file_perms getattr read execute open ioctl };
allow init adb_data_file:lnk_file { create_file_perms read };

# Allow init to access app data files in ADB context (for LSPosed logs, etc.)
allow init app_data_file:dir { search read open getattr };
allow init app_data_file:file { getattr read open };

# Allow init to execute dex2oat binaries from Magisk modules
allow init dex2oat_exec:file { getattr read execute open };

# Allow kernel to access ADB data for debugging and module loading
allow kernel adb_data_file:dir { search read open getattr };
allow kernel adb_data_file:file { getattr read execute open };

# Allow system_server to interact with root management tools when needed
allow system_server adb_data_file:dir { search read open getattr };
allow system_server adb_data_file:file { getattr read open };

# Allow adbd to access its own data directory
allow adbd adb_data_file:dir { create_dir_perms search read open getattr };
allow adbd adb_data_file:file { create_file_perms getattr read execute open };

# Allow shell to access ADB data for debugging
allow shell adb_data_file:dir { search read open getattr };
allow shell adb_data_file:file { getattr read execute open };

# Allow untrusted apps to read basic ADB data (for root detection, etc.)
allow untrusted_app adb_data_file:dir { search getattr };
allow untrusted_app adb_data_file:file { getattr };

# Allow system apps to interact with root management
allow system_app adb_data_file:dir { search read open getattr };
allow system_app adb_data_file:file { getattr read open };

# Allow platform apps to interact with root management
allow platform_app adb_data_file:dir { search read open getattr };
allow platform_app adb_data_file:file { getattr read open };

# Allow priv_app to interact with root management (for root managers, etc.)
allow priv_app adb_data_file:dir { search read open getattr };
allow priv_app adb_data_file:file { getattr read open };

# Allow zygote to access ADB data for module injection
allow zygote adb_data_file:dir { search read open getattr };
allow zygote adb_data_file:file { getattr read open };

# Allow app_zygote to access ADB data for module injection
allow app_zygote adb_data_file:dir { search read open getattr };
allow app_zygote adb_data_file:file { getattr read open };

# Allow webview_zygote to access ADB data if needed
allow webview_zygote adb_data_file:dir { search getattr };
allow webview_zygote adb_data_file:file { getattr };

# Allow vold to access ADB data during mount operations
allow vold adb_data_file:dir { search read open getattr };
allow vold adb_data_file:file { getattr read open };

# Allow installd to access ADB data for package operations
allow installd adb_data_file:dir { search read open getattr };
allow installd adb_data_file:file { getattr read open };

# Allow dumpstate to access ADB data for debugging
allow dumpstate adb_data_file:dir { search read open getattr };
allow dumpstate adb_data_file:file { getattr read open };

# Allow logd to access ADB data for logging
allow logd adb_data_file:dir { search getattr };
allow logd adb_data_file:file { getattr };

# Allow surfaceflinger to access ADB data if needed for modules
allow surfaceflinger adb_data_file:dir { search getattr };
allow surfaceflinger adb_data_file:file { getattr };

# Allow mediaserver to access ADB data if needed for modules
allow mediaserver adb_data_file:dir { search getattr };
allow mediaserver adb_data_file:file { getattr };

# Allow audioserver to access ADB data if needed for modules
allow audioserver adb_data_file:dir { search getattr };
allow audioserver adb_data_file:file { getattr };

# Allow cameraserver to access ADB data if needed for modules
allow cameraserver adb_data_file:dir { search getattr };
allow cameraserver adb_data_file:file { getattr };

# Allow vendor_init to access ADB data during boot
allow vendor_init adb_data_file:dir { search read open getattr };
allow vendor_init adb_data_file:file { getattr read open };

# Allow recovery to access ADB data for recovery operations
allow recovery adb_data_file:dir { create_dir_perms search read open getattr };
allow recovery adb_data_file:file { create_file_perms getattr read execute open };

# Allow fastbootd to access ADB data
allow fastbootd adb_data_file:dir { search read open getattr };
allow fastbootd adb_data_file:file { getattr read open };

# Allow toolbox to access ADB data for system utilities
allow toolbox adb_data_file:dir { search read open getattr };
allow toolbox adb_data_file:file { getattr read execute open };

# Allow su to access ADB data (for root shells)
allow su adb_data_file:dir { create_dir_perms search read open getattr };
allow su adb_data_file:file { create_file_perms getattr read execute open };

# Allow magisk daemon to access its own data
allow magisk adb_data_file:dir { create_dir_perms search read open getattr };
allow magisk adb_data_file:file { create_file_perms getattr read execute open };

# Allow magisk to transition to other domains when needed
allow magisk init:process { transition };
allow magisk shell:process { transition };
allow magisk su:process { transition };

# Allow property access for root management
allow init adb_data_file:property_service set;
allow shell adb_data_file:property_service set;
allow su adb_data_file:property_service set;
