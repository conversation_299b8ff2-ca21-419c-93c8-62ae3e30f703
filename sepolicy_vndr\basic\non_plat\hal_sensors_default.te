# graphics allocator
allow hal_sensors_default hal_graphics_allocator_default:fd use;

# gpu device
allow hal_sensors_default gpu_device:dir create_dir_perms;
allow hal_sensors_default gpu_device:chr_file rw_file_perms;
allow hal_sensors_default dri_device:chr_file rw_file_perms;

# ion device
allow hal_sensors_default ion_device:dir create_dir_perms;
allow hal_sensors_default ion_device:chr_file rw_file_perms;

# sensors input rw access
allow hal_sensors_default sysfs_sensor:dir r_dir_perms;
allow hal_sensors_default sysfs_sensor:file rw_file_perms;

# hal sensor for chr_file
allow hal_sensors_default hwmsensor_device:chr_file r_file_perms;

# Access sensor bio devices
allow hal_sensors_default sensorlist_device:chr_file rw_file_perms;
allow hal_sensors_default m_acc_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_als_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_ps_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_mag_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_gyro_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_baro_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_hmdy_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_act_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_pedo_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_situ_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_step_c_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_fusion_misc_device:chr_file rw_file_perms;
allow hal_sensors_default m_bio_misc_device:chr_file rw_file_perms;
allow hal_sensors_default hf_manager_device:chr_file rw_file_perms;

# Access mtk sensor setting and calibration node.
# for data
allow hal_sensors_default sensor_data_file:file create_file_perms;
allow hal_sensors_default sensor_data_file:dir create_dir_perms;

# for nvcfg
allow hal_sensors_default nvcfg_file:file create_file_perms;
allow hal_sensors_default nvcfg_file:dir create_dir_perms;

# Date : WK18.21
# Operation: P migration
# Purpose: Allow to search /mnt/vendor/nvdata for fstab when using NVM_Init()
allow hal_sensors_default mnt_vendor_file:dir search;

# Date : WK20.25
# Purpose: Allow to read /bus/platform/drivers/mtk_nanohub/state
allow hal_sensors_default sysfs_mtk_nanohub_state:file r_file_perms;

# Allow hal_sensors_default to access sysfs_scp
allow hal_sensors_default sysfs_scp:dir search;
allow hal_sensors_default sysfs_scp:file rw_file_perms;

# call into system_server process (callbacks)
binder_call(hal_sensors_default, system_server)
