# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : WK14.38
# Operation : Migration
# Purpose : run guitar_update for touch F/W upgrade.
allow kernel sdcard_type:dir search;

# Date : WK14.39
# Operation : Migration
# Purpose : ums driver can access blk_file
allow kernel loop_device:blk_file r_file_perms;
allow kernel vold_device:blk_file rw_file_perms;

# Date : WK15.35
# Operation : Migration
# Purpose : grant fon_image_data_file read permission for loop device
allow kernel fon_image_data_file:file read;

# Date : WK15.38
# Operation : Migration
# Purpose : grant proc_thermal for dir search
allow kernel proc_thermal:dir search;

# Date : WK16.11
# Operation : Migration
# Purpose : grant storage_file and wifi_data_file for kernel thread mtk_wmtd to access /sdcard/wifi.cfg
# and /data/misc/wifi/wifi.cfg to access wifi.cfg, in which, some wifi driver configuations are there.
allow kernel mnt_user_file:dir search;
allow kernel mnt_user_file:lnk_file r_file_perms;
allow kernel wifi_data_file:file r_file_perms;
allow kernel wifi_data_file:dir search;
allow kernel storage_file:lnk_file r_file_perms;
allow kernel sdcard_type:file open;

# Data : WK16.16
# Operation : Migration
# Purpose :  Access to TC1 partition for reading MEID
allow kernel block_device:dir search;

# Data : WK16.16
# Operation : Migration
# Purpose :  Access to TC1 partition for reading MEID
allow kernel misc2_block_device:blk_file rw_file_perms;

# Date : WK16.30
# Operation: SQC
# Purpose: Allow sdcardfs workqueue to access lower file systems
allow kernel fuseblk:dir create_dir_perms;
allow kernel fuseblk:file create_file_perms;

# Date : WK16.30
# Operation: SQC
# Purpose: Allow sdcardfs workqueue to access lower file systems
allow kernel {vfat mnt_media_rw_file}:dir create_dir_perms;
allow kernel {vfat mnt_media_rw_file}:file create_file_perms;
allow kernel kernel:key { write search setattr };

# Date : WK16.42
# Operation: SQC
# Purpose: Allow task of cpuset cgroup can migration to parent cgroup when cpus is NULL
allow kernel platform_app:process setsched;

# Date : WK17.01
# Operation: SQC
# Purpose: Allow OpenDSP kthread to write debug dump to sdcard
allow kernel audioserver:fd use;

# Date : WK18.02
# Operation: SQC
# Purpose: Allow SCP SmartPA kthread to write debug dump to sdcard
allow kernel hal_audio_default:fd use;
allow kernel factory:fd use;

# Date : WK18.29
# Operation: SQC
# Purpose: Allow kernel read firmware binary on vendor partition
allow kernel vendor_file:file r_file_perms;

# Date : WK18.35
# Operation: SQC
# Purpose: Allow VOW kthread to write debug PCM dump
allow kernel mtk_audiohal_data_file:file write;

# Date: WK19.03
allow kernel expdb_block_device:blk_file rw_file_perms;

# b/220801802
allow kernel same_process_hal_file:file r_file_perms;
