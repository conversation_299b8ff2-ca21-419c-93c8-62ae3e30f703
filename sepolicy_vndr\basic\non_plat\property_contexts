# ==============================================
# Common SEPolicy Rule
# ==============================================

ctl.vendor.gsm0710muxd u:object_r:vendor_mtk_ctl_gsm0710muxd_prop:s0

vendor.ril.ipo u:object_r:vendor_mtk_ril_ipo_prop:s0

vendor.usb.         u:object_r:vendor_mtk_usb_prop:s0
persist.vendor.usb. u:object_r:vendor_mtk_usb_prop:s0

vendor.ril.mux. u:object_r:vendor_mtk_gsm0710muxd_prop:s0

ctl.vendor.ril-daemon-mtk u:object_r:vendor_mtk_ctl_ril-daemon-mtk_prop:s0
ctl.vendor.fusion_ril_mtk u:object_r:vendor_mtk_ctl_fusion_ril_mtk_prop:s0
ctl.vendor.ril-proxy      u:object_r:vendor_mtk_ctl_ril-proxy_prop:s0
ctl.vendor.viarild        u:object_r:vendor_mtk_ctl_viarild_prop:s0

ctl.vendor.muxreport-daemon u:object_r:vendor_mtk_ctl_muxreport-daemon_prop:s0
ctl.vendor.ccci_fsd         u:object_r:vendor_mtk_ctl_ccci_fsd_prop:s0
ctl.vendor.ccci2_fsd        u:object_r:vendor_mtk_ctl_ccci2_fsd_prop:s0
ctl.vendor.ccci3_fsd        u:object_r:vendor_mtk_ctl_ccci3_fsd_prop:s0

vendor.ril.active.md       u:object_r:vendor_mtk_ril_active_md_prop:s0
vendor.ril.mux.report.case u:object_r:vendor_mtk_ril_mux_report_case_prop:s0
vendor.ril.cdma.report     u:object_r:vendor_mtk_ril_cdma_report_prop:s0

# dynamic telephony switch
ro.boot.opt_md2_support    u:object_r:vendor_mtk_tel_switch_prop:s0
ro.boot.opt_md5_support    u:object_r:vendor_mtk_tel_switch_prop:s0
ro.boot.opt_sim_count      u:object_r:vendor_mtk_tel_switch_prop:s0
ro.boot.opt_using_default  u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_c2k_lte_mode u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_c2k_support  u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_eccci_c2k    u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_lte_support  u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_md1_support  u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_md3_support  u:object_r:vendor_mtk_tel_switch_prop:s0
ro.vendor.mtk_ps1_rat      u:object_r:vendor_mtk_tel_switch_prop:s0

vendor.gps.clock.type   u:object_r:vendor_mtk_mnld_prop:s0
vendor.gps.gps.version  u:object_r:vendor_mtk_mnld_prop:s0
vendor.gpsdbglog.enable u:object_r:vendor_mtk_mnld_prop:s0
vendor.gpsdbglog.       u:object_r:vendor_mtk_mnld_prop:s0
vendor.debug.gps.       u:object_r:vendor_mtk_mnld_prop:s0

ro.vendor.audio.         u:object_r:vendor_mtk_audio_prop:s0
vendor.audio.            u:object_r:vendor_mtk_audio_prop:s0
persist.vendor.audio.    u:object_r:vendor_mtk_audio_prop:s0
vendor.streamout.        u:object_r:vendor_mtk_audiohal_prop:s0
vendor.streamin.         u:object_r:vendor_mtk_audiohal_prop:s0
vendor.a2dp.             u:object_r:vendor_mtk_audiohal_prop:s0
vendor.audiohal.         u:object_r:vendor_mtk_audiohal_prop:s0
persist.vendor.audiohal. u:object_r:vendor_mtk_audiohal_prop:s0
persist.vendor.vow.      u:object_r:vendor_mtk_audiohal_prop:s0

persist.vendor.connsys.coredump.mode u:object_r:vendor_mtk_coredump_prop:s0
persist.vendor.connsys.              u:object_r:vendor_mtk_wmt_prop:s0
vendor.connsys.                      u:object_r:vendor_mtk_wmt_prop:s0

# c2k_prop
vendor.net.cdma.mdmstat u:object_r:vendor_mtk_net_cdma_mdmstat_prop:s0

# md status
vendor.mtk.md u:object_r:vendor_mtk_md_prop:s0

# sms
vendor.sms.md.ready	    u:object_r:vendor_mtk_md_prop:s0

# factory idle current prop
vendor.debug.factory.idle_state u:object_r:vendor_mtk_factory_idle_state_prop:s0

vendor.service.nvram_init u:object_r:vendor_mtk_service_nvram_init_prop:s0
vendor.service.nvram_restore u:object_r:vendor_mtk_service_nvram_restore_prop:s0

# Camera APP Mode
vendor.client. u:object_r:vendor_mtk_em_prop:s0

vendor.debug.camera.p2plug.log u:object_r:vendor_mtk_mediatek_prop:s0
vendor.client.em.appmode       u:object_r:vendor_mtk_mediatek_prop:s0

# EM test/debug purpose
persist.vendor.em.hidl. u:object_r:vendor_mtk_em_hidl_prop:s0

# ims operator property
vendor.ril.volte.mal.pctid u:object_r:vendor_mtk_operator_id_prop:s0

persist.vendor.radio.simswitch.emmode u:object_r:vendor_mtk_simswitch_emmode_prop:s0

persist.vendor.radio.mtk_dsbp_support u:object_r:vendor_mtk_dsbp_support_prop:s0

persist.vendor.radio.imstestmode u:object_r:vendor_mtk_imstestmode_prop:s0

persist.vendor.radio.smsformat u:object_r:vendor_mtk_smsformat_prop:s0

persist.vendor.radio.gprs.prefer u:object_r:vendor_mtk_gprs_prefer_prop:s0

persist.vendor.radio.testsim.cardtype u:object_r:vendor_mtk_testsim_cardtype_prop:s0

persist.vendor.radio.ct.ir.engmode u:object_r:vendor_mtk_ct_ir_engmode_prop:s0

persist.vendor.radio.disable_c2k_cap u:object_r:vendor_mtk_disable_c2k_cap_prop:s0

# modem reset delay property
vendor.mediatek.debug.md.reset.wait u:object_r:vendor_mtk_debug_md_reset_prop:s0

# video c2 log property
vendor.mtk.c2   u:object_r:vendor_mtk_c2_log_prop:s0

# video log omx.* property
vendor.mtk.omx. u:object_r:vendor_mtk_omx_log_prop:s0

vendor.mtk.vdec.log u:object_r:vendor_mtk_vdec_log_prop:s0

vendor.mtk.vdectlc.log u:object_r:vendor_mtk_vdectlc_log_prop:s0

vendor.mtk.venc.h264.showlog u:object_r:vendor_mtk_venc_h264_showlog_prop:s0

persist.vendor.radio.modem.warning u:object_r:vendor_mtk_modem_warning_prop:s0

persist.vendor.meta.connecttype u:object_r:vendor_mtk_meta_connecttype_prop:s0

# Telephony Sensitive property
vendor.ril.iccid.sim                          u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.uim.subscriberid                   u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.last_iccid_sim           u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.ia.iccid                           u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.radio.ia                           u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.c2kirat.ia.sim1                    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.c2kirat.ia.sim2                    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.c2kirat.ia.sim3                    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
vendor.ril.c2kirat.ia.sim4                    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ia                       u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ia.1                     u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ia.2                     u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ia.3                     u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.data.iccid               u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.mobile.data              u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ls1icid                  u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ls2icid                  u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ls3icid                  u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ls4icid                  u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.mtk.provision.mccmnc.          u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ss.hashed_last_iccid1    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ss.hashed_last_iccid2    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ss.hashed_last_iccid3    u:object_r:vendor_mtk_telephony_sensitive_prop:s0
persist.vendor.radio.ss.hashed_last_iccid4    u:object_r:vendor_mtk_telephony_sensitive_prop:s0

# change thermal config
vendor.thermal.manager.data u:object_r:vendor_mtk_thermal_config_prop:s0

vendor.debug.sf.hwc_pid           u:object_r:vendor_mtk_graphics_hwc_pid_prop:s0
vendor.debug.sf.hdr_enable        u:object_r:vendor_mtk_graphics_hwc_hdr_prop:s0
vendor.debug.sf.validate_separate u:object_r:vendor_mtk_graphics_hwc_validate_separate_prop:s0
vendor.debug.sf.latch_unsignaled  u:object_r:vendor_mtk_graphics_hwc_latch_unsignaled_prop:s0

# sf vendor cpupolicy config
vendor.debug.sf.cpupolicy  u:object_r:vendor_mtk_debug_sf_cpupolicy_prop:s0
vendor.debug.sf.cpupolicy. u:object_r:vendor_mtk_debug_sf_cpupolicy_prop:s0

# atm modem mode property(ATM)
persist.vendor.atm.mdmode u:object_r:vendor_mtk_atm_mdmode_prop:s0

# atm ip address property(ATM)
persist.vendor.atm.ipaddress u:object_r:vendor_mtk_atm_ipaddr_prop:s0

# atm boot property(ATM)
ro.boot.atm u:object_r:vendor_mtk_default_prop:s0

# telephony property
vendor.ril.           u:object_r:vendor_mtk_radio_prop:s0
ro.vendor.ril.        u:object_r:vendor_mtk_radio_prop:s0
vendor.gsm.           u:object_r:vendor_mtk_radio_prop:s0
persist.vendor.radio. u:object_r:vendor_mtk_radio_prop:s0

persist.vendor.mtk_ct_volte_support u:object_r:vendor_mtk_ct_volte_prop:s0

ro.vendor.mtk_ril_mode u:object_r:vendor_mtk_ril_mode_prop:s0

# GPS support properties
ro.vendor.mtk_gps_support        u:object_r:vendor_mtk_gps_support_prop:s0
ro.vendor.mtk_agps_app           u:object_r:vendor_mtk_gps_support_prop:s0
ro.vendor.mtk_log_hide_gps       u:object_r:vendor_mtk_gps_support_prop:s0
ro.vendor.mtk_hidl_consolidation u:object_r:vendor_mtk_gps_support_prop:s0

# MTK GMS
ro.vendor.soc u:object_r:vendor_mtk_soc_prop:s0

# rat config
ro.vendor.mtk_protocol1_rat_config u:object_r:vendor_mtk_rat_config_prop:s0

# mtk aal
ro.vendor.mtk_aal_support           u:object_r:vendor_mtk_aal_ro_prop:s0
ro.vendor.mtk_ultra_dimming_support u:object_r:vendor_mtk_aal_ro_prop:s0
ro.vendor.mtk_dre30_support         u:object_r:vendor_mtk_aal_ro_prop:s0

# mtk pq
persist.vendor.sys.pq.             u:object_r:vendor_mtk_pq_prop:s0
vendor.debug.pq.                   u:object_r:vendor_mtk_pq_prop:s0
vendor.sys.pq.                     u:object_r:vendor_mtk_pq_prop:s0
persist.vendor.sys.isp.            u:object_r:vendor_mtk_pq_prop:s0
persist.vendor.sys.mtkaal.         u:object_r:vendor_mtk_pq_prop:s0
ro.vendor.mtk_pq_color_mode        u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.mtk_blulight_def_support u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.mtk_chameleon_support    u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.mtk_pq_support           u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.mtk_appgamepq_support    u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.mtk_gamehdr_support      u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.globalpq.support         u:object_r:vendor_mtk_pq_ro_prop:s0
ro.vendor.pq.                      u:object_r:vendor_mtk_pq_ro_prop:s0

# mtk display
ro.vendor.mtk_ovl     u:object_r:vendor_mtk_display_ro_prop:s0

# Mtk properties that allow all system/vendor processes to read.
# Usually they are config properties (but not limited to)
ro.vendor.mtk_tdd_data_only_support  u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_audio_alac_support     u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_support_mp2_playback   u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_audio_ape_support      u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_flv_playback_support   u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_mtkps_playback_support u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_wearable_platform      u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mediatek.platform          u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mediatek.version.branch    u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mediatek.version.release   u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_exchange_support       u:object_r:vendor_mtk_default_prop:s0
vendor.met.running                   u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_disable_cap_switch     u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_sim_card_onoff         u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_perf_plus              u:object_r:vendor_mtk_default_prop:s0
ro.vendor.pref_scale_enable_cfg      u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_flight_mode_power_off_md  u:object_r:vendor_mtk_default_prop:s0

# mtk emmc
ro.vendor.mtk_emmc_support u:object_r:vendor_mtk_emmc_support_prop:s0

# MTK connsys log feature
ro.vendor.connsys.dedicated.log u:object_r:vendor_mtk_default_prop:s0

# em usb property
vendor.usb.port.mode u:object_r:vendor_mtk_em_usb_prop:s0
vendor.em.usb.       u:object_r:vendor_mtk_em_usb_prop:s0

persist.vendor.usb.otg.switch u:object_r:vendor_mtk_usb_otg_switch_prop:s0

# mtk rsc
ro.boot.rsc u:object_r:vendor_mtk_default_prop:s0

# mtk anr property
persist.vendor.dbg.anrflow u:object_r:vendor_mtk_anr_support_prop:s0
persist.vendor.anr.        u:object_r:vendor_mtk_anr_support_prop:s0
vendor.anr.autotest        u:object_r:vendor_mtk_anr_support_prop:s0

# mtk app resolution tuner
ro.vendor.app_resolution_tuner u:object_r:vendor_mtk_appresolutiontuner_prop:s0
persist.vendor.dbg.disable.art u:object_r:vendor_mtk_appresolutiontuner_prop:s0

# mtk fullscreen switch
ro.vendor.fullscreen_switch u:object_r:vendor_mtk_fullscreenswitch_prop:s0

# ims xcap property
persist.vendor.ss. u:object_r:vendor_mtk_ss_vendor_prop:s0

# MTK App feature
ro.vendor.net.upload.mark.default u:object_r:vendor_mtk_app_prop:s0

# malloc debug unwind backtrace switch property
vendor.debug.malloc.bt.switch u:object_r:vendor_mtk_malloc_debug_backtrace_prop:s0

ro.vendor.gmo.ram_optimize         u:object_r:vendor_mtk_default_prop:s0
ro.vendor.gmo.rom_optimize         u:object_r:vendor_mtk_default_prop:s0
ro.vendor.mtk_config_max_dram_size u:object_r:vendor_mtk_default_prop:s0

# MTK Voice Recognize property
vendor.voicerecognize.raw      u:object_r:vendor_mtk_voicerecgnize_prop:s0
vendor.voicerecognize_data.raw u:object_r:vendor_mtk_voicerecgnize_prop:s0
vendor.voicerecognize.noDL     u:object_r:vendor_mtk_voicerecgnize_prop:s0

# mtk bt enable SAP profile property
ro.vendor.mtk.bt_sap_enable u:object_r:vendor_mtk_bt_sap_enable_prop:s0

# powerhal config
persist.vendor.powerhal. u:object_r:vendor_power_prop:s0
vendor.powerhal.gpu.     u:object_r:vendor_mtk_powerhal_gpu_prop:s0

# MTK Wifi wlan_assistant property
vendor.mtk.nvram.ready u:object_r:vendor_mtk_nvram_ready_prop:s0

# Wi-Fi Hotspot
ro.vendor.wifi.sap.interface u:object_r:vendor_mtk_wifi_hotspot_prop:s0
ro.vendor.wifi.sap.concurrent.iface u:object_r:vendor_mtk_wifi_hotspot_prop:s0

# Wi-Fi HAL
vendor.wlan.firmware.version u:object_r:vendor_mtk_wifi_hal_prop:s0
vendor.wlan.driver.version u:object_r:vendor_mtk_wifi_hal_prop:s0

# mtk hdmi
persist.vendor.sys.hdmi_hidl. u:object_r:vendor_mtk_hdmi_prop:s0

# mtk nn option
ro.vendor.mtk_nn.option u:object_r:vendor_mtk_nn_option_prop:s0

# mtk gbe
vendor.performance.gbe u:object_r:vendor_mtk_gbe_prop:s0

# system wfc service property
persist.vendor.wfc. u:object_r:vendor_mtk_wfc_serv_prop:s0

# config no bt consys chip
ro.vendor.bluetooth.noconsyschip u:object_r:vendor_mtk_default_prop:s0

# mtk gpu property
vendor.debug.gpu.  u:object_r:vendor_mtk_gpu_prop:s0
vendor.debug.gpud. u:object_r:vendor_mtk_gpu_prop:s0
vendor.mali.config u:object_r:vendor_mtk_gpu_prop:s0
ro.vendor.game_aisr_enable u:object_r:vendor_mtk_gpu_prop:s0
ro.vendor.mtk.gpu. u:object_r:vendor_mtk_gpu_prop:s0

# mtk aod property
ro.vendor.mtk_aod_support u:object_r:vendor_mtk_aod_support_prop:s0

# mtk hwc property
ro.vendor.composer_version u:object_r:vendor_mtk_default_prop:s0

#============= mtk sensor property ==============
ro.vendor.init.sensor.rc u:object_r:vendor_mtk_sensor_prop:s0
ro.vendor.mtk.sensor.support u:object_r:vendor_mtk_sensor_prop:s0
ro.vendor.mag.calibration.in.sensorhub u:object_r:vendor_mtk_sensor_prop:s0
ro.vendor.fusion.algorithm.in.sensorhub u:object_r:vendor_mtk_sensor_prop:s0

vendor.bluetooth.ldac.abr u:object_r:vendor_mtk_default_prop:s0

# input resample latency property
ro.vendor.input.resample_latency_ms u:object_r:vendor_mtk_input_resample_latency_prop:s0

# allow input report rate property
ro.vendor.input.touch_report_rate u:object_r:vendor_mtk_input_report_rate_prop:s0

# video property
vendor.mtkomx.color.convert u:object_r:vendor_mtk_video_prop:s0
vendor.mtk.c2.vdec.fmt.disabled.whitelist u:object_r:vendor_mtk_video_prop:s0

# config no bt multidevice performance according to chip
ro.vendor.bluetooth.bt_multidevice_perform u:object_r:vendor_mtk_default_prop:s0

# factory mode property
persist.vendor.factory. u:object_r:vendor_mtk_factory_prop:s0
vendor.mtk.factory.start u:object_r:vendor_mtk_factory_start_prop:s0

#=============mtk loading module property====================
vendor.all.modules.ready u:object_r:vendor_mtk_device_prop:s0

#=============mtk wifi driver log property====================
ro.vendor.wlan.standalone.log u:object_r:vendor_mtk_default_prop:s0

#=============mtk eara==============
vendor.performance.frs u:object_r:vendor_mtk_frs_prop:s0

# mtkperf powerhal memory property
vendor.sys.vm.      u:object_r:vendor_mtk_vm_prop:s0

# mediaserver support 64-bit
ro.vendor.mtk_prefer_64bit_proc u:object_r:vendor_mtk_prefer64_prop:s0

#=============mtk bt property====================
ro.vendor.bluetooth.a2dp_aac_vbr.is_disabled           u:object_r:vendor_mtk_bt_aac_vbr_prop:s0
persist.vendor.bluetooth.leaudio_mode                  u:object_r:vendor_mtk_default_prop:s0
persist.vendor.bluetooth.a2dp_src_sink_both_enable     u:object_r:vendor_mtk_default_prop:s0
persist.vendor.bluetooth.blemesh_enable                u:object_r:vendor_mtk_default_prop:s0
persist.vendor.bluetooth.mtk_bt_consumer_feature       u:object_r:vendor_mtk_default_prop:s0
persist.vendor.bluetooth.fw_log_switch                 u:object_r:vendor_mtk_default_prop:s0

vendor.bluetooth.performance. u:object_r:vendor_mtk_bt_perf_prop:s0

# display debug log property
persist.vendor.debug.hwc.log     u:object_r:vendor_mtk_hwc_debug_log_prop:s0
vendor.debug.hwc.                u:object_r:vendor_mtk_hwc_debug_log_prop:s0
vendor.dp.log.enable             u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.frameChange.disable    u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.systrace.enable        u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.dumpbuffer.enable      u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.dumpbuffer.error       u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.dumpbuffer.folder      u:object_r:vendor_mtk_mdp_debug_log_prop:s0
persist.vendor.dp.dumpreg.check  u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.dumpreg.enable         u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.dp.mmpath.enable          u:object_r:vendor_mtk_mdp_debug_log_prop:s0
vendor.debug.logger.             u:object_r:vendor_debug_logger_prop:s0

# MDP properties
vendor.dp.met.enable          u:object_r:vendor_mtk_mdp_prop:s0
vendor.dp.dualpipe.4k.enable  u:object_r:vendor_mtk_mdp_prop:s0
vendor.dp.dualpipe.cam.enable u:object_r:vendor_mtk_mdp_prop:s0
persist.vendor.dp.met.check   u:object_r:vendor_mtk_mdp_prop:s0

# neuropilot flag
ro.vendor.neuropilot.flag u:object_r:vendor_mtk_neuropilot_flag_prop:s0

#=============mtk eara_io property====================
persist.vendor.eara_io.          u:object_r:vendor_mtk_eara_io_prop:s0

# xfrm and mdrsra property for non 5G GKI platform
persist.vendor.mdrsra_v2_support u:object_r:vendor_mtk_mdrsra_v2_support_prop:s0
persist.vendor.xfrm_support u:object_r:vendor_mtk_xfrm_support_prop:s0

# Thermal
vendor.thermal. u:object_r:vendor_thermal_prop:s0
