<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2024 VoltageOS
     SPDX-License-Identifier: Apache-2.0
-->
<resources>
    <!-- Whether device supports an alt. ambient display -->
    <bool name="config_alt_ambient_display">false</bool>

    <!-- Whether Pixel props is enabled -->
    <bool name="config_enablePixelProps">true</bool>

    <!-- Whether device has VOOC charging support -->
    <bool name="config_hasVoocCharger">true</bool>
    
    <!-- Path to fast charging status file to detect whether an oem fast charger is active -->
    <string name="config_oemFastChargerStatusPath" translatable="false">/sys/class/oplus_chg/battery/fast_charge</string>

    <!-- Path to fast charging status file to detect whether an oem fast charger is active -->
    <string name="config_oemFastChargerStatusPath2" translatable="false">/sys/class/oplus_chg/battery/voocchg_ing</string>

    <!-- Expected value from fast charging status file  -->
    <string name="config_oemFastChargerStatusValue" translatable="false">1</string>

    <!-- Smart charging -->
    <string name="config_SmartChargingSysfsNode" translatable="false">/sys/class/oplus_chg/battery/mmi_charging_enable</string>
    <string name="config_SmartChargingSuspendValue" translatable="false">0</string>
    <string name="config_SmartChargingResumeValue" translatable="false">1</string>

    <!-- Whether the device supports Smart Pixels -->
    <bool name="config_supportSmartPixels">true</bool>

    <!-- Should we listen for fingerprints when the screen is off?  Devices
         with a rear-mounted sensor want this, but certain devices have
         the sensor embedded in the power key and listening all the time
         causes a poor experience. -->
    <bool name="config_fingerprintWakeAndUnlock">false</bool>

    <bool name="config_defaultAntiFlicker">true</bool>

    <!-- Whether devices suports in-display fingerprint when screen is off -->
    <bool name="config_supportScreenOffUdfps">true</bool>

  <!-- Whether charging control should be enabled by default -->
  <bool name="config_chargingControlEnabled">true</bool>

</resources>
