<compatibility-matrix version="5.0" type="framework">
    <hal format="aidl" optional="true">
        <name>android.hardware.camera.provider</name>
        <interface>
            <name>ICameraProvider</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.media.c2</name>
        <version>1.0</version>
        <interface>
            <name>IComponentStore</name>
	    <instance>software</instance>
            <instance>default</instance>
	    <instance>ozoaudio</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.nxp.nxpnfc_aidl</name>
        <interface>
            <name>INxpNfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.radio</name>
        <version>1.2</version>
        <version>1.6</version>
        <interface>
            <name>IRadio</name>
            <instance>em1</instance>
            <instance>em2</instance>
            <instance>em3</instance>
            <instance>em4</instance>
            <instance>imsAospSlot1</instance>
            <instance>imsAospSlot2</instance>
            <instance>imsAospSlot3</instance>
            <instance>imsAospSlot4</instance>
            <instance>se1</instance>
            <instance>se2</instance>
            <instance>se3</instance>
            <instance>se4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
        <interface>
            <name>ISap</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.radio.config</name>
        <version>1.3</version>
        <interface>
            <name>IRadioConfig</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.config</name>
        <interface>
            <name>IRadioConfig</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.data</name>
        <interface>
            <name>IRadioData</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.messaging</name>
        <interface>
            <name>IRadioMessaging</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.modem</name>
        <interface>
            <name>IRadioModem</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>se1</instance>
            <instance>se2</instance>
            <instance>se3</instance>
            <instance>se4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.network</name>
        <interface>
            <name>IRadioNetwork</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.sim</name>
        <interface>
            <name>IRadioSim</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>se1</instance>
            <instance>se2</instance>
            <instance>se3</instance>
            <instance>se4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>android.hardware.radio.voice</name>
        <interface>
            <name>IRadioVoice</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>arm.mali.platform</name>
        <interface>
            <name>ICompression</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.display.PanelChaplin</name>
        <version>1.0</version>
        <interface>
            <name>IPanelChaplin</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.apmonitor</name>
        <version>2.0</version>
        <interface>
            <name>IApmService</name>
            <instance>apm_hidl_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.apuware.apusys</name>
        <version>1.0</version>
        <version>2.0</version>
        <version>2.1</version>
        <interface>
            <name>INeuronApusys</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.apuware.apusys</name>
        <interface>
            <name>INeuronApusys</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.apuware.hmp</name>
        <version>1.0</version>
        <interface>
            <name>IApuwareHmp</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.apuware.utils</name>
        <version>1.0</version>
        <version>2.0</version>
        <interface>
            <name>IApuwareUtils</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.apuware.xrp</name>
        <version>1.0</version>
        <version>2.0</version>
        <interface>
            <name>INeuronXrp</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.atci</name>
        <version>1.0</version>
        <interface>
            <name>IAtcid</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.bluetooth.audio</name>
        <version>2.0-2</version>
        <interface>
            <name>IBluetoothAudioProvidersFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.camera.aovservice</name>
        <interface>
            <name>IAovService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.camera.atms</name>
        <version>1.0</version>
        <interface>
            <name>IATMs</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.camera.bgservice</name>
        <version>1.1</version>
        <interface>
            <name>IBGService</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.camera.ccap</name>
        <version>1.0</version>
        <interface>
            <name>ICCAPControl</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.camera.isphal</name>
        <version>1.0-1</version>
        <interface>
            <name>IISPModule</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true"> 
        <name>android.hardware.secure_element</name> 
        <version>1.0-2</version> 
        <interface>
            <name>ISecureElement</name> 
            <instance>SIM1</instance> 
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.camera.postproc</name>
        <version>1.0</version>
        <interface>
            <name>IPostDevice</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.clientapi</name>
        <version>1.0</version>
        <interface>
            <name>IClientapi</name>
            <instance>clientapi_hal_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.composer_ext</name>
        <version>1.0</version>
        <interface>
            <name>IComposerExt</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.dfps</name>
        <version>1.0</version>
        <interface>
            <name>IFpsPolicyService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.dmc</name>
        <version>1.2</version>
        <interface>
            <name>IDmcService</name>
            <instance>dmc_hidl_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.dplanner</name>
        <version>2.0</version>
        <interface>
            <name>IDPlanner</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.engineermode</name>
        <version>1.3</version>
        <interface>
            <name>IEmd</name>
            <instance>EmHidlServer</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.engineermode</name>
        <interface>
            <name>IEmds</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.gnss.batching</name>
        <interface>
            <name>IMtkGnssBatching</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.gpu</name>
        <version>1.0</version>
        <interface>
            <name>IGraphicExt</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.hdmi</name>
        <version>1.3</version>
        <interface>
            <name>IMtkHdmiService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.imsa</name>
        <version>1.0</version>
        <interface>
            <name>IImsa</name>
            <instance>imsa</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.interfaces_tc1.mtkradioex_tc1</name>
        <version>3.0</version>
        <interface>
            <name>IMtkRadioEx</name>
            <instance>mtkAssist1</instance>
            <instance>mtkAssist2</instance>
            <instance>mtkCap1</instance>
            <instance>mtkCap2</instance>
            <instance>mtkEm1</instance>
            <instance>mtkEm2</instance>
            <instance>mtkSe1</instance>
            <instance>mtkSe2</instance>
            <instance>mtkSlot1</instance>
            <instance>mtkSlot2</instance>
            <instance>mtkSmartRatSwitch1</instance>
            <instance>mtkSmartRatSwitch2</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.keymanage</name>
        <version>1.0</version>
        <interface>
            <name>IKeymanage</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.keymaster_attestation</name>
        <version>1.1</version>
        <interface>
            <name>IKeymasterDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.lbs</name>
        <version>1.0</version>
        <interface>
            <name>ILbs</name>
            <instance>AgpsDebugInterface</instance>
            <instance>AgpsInterface</instance>
            <instance>mtk_agps2framework</instance>
            <instance>mtk_agpsd2debugService</instance>
            <instance>mtk_debugService2agpsd</instance>
            <instance>mtk_debugService2mnld</instance>
            <instance>mtk_framework2agps</instance>
            <instance>mtk_jam2mnl</instance>
            <instance>mtk_lbs_log_v2s</instance>
            <instance>mtk_lppe_socket_agps</instance>
            <instance>mtk_lppe_socket_bt</instance>
            <instance>mtk_lppe_socket_ipaddr</instance>
            <instance>mtk_lppe_socket_lbs</instance>
            <instance>mtk_lppe_socket_network</instance>
            <instance>mtk_lppe_socket_sensor</instance>
            <instance>mtk_lppe_socket_wlan</instance>
            <instance>mtk_meta2mnld</instance>
            <instance>mtk_mnl2jam</instance>
            <instance>mtk_mnld2debugService</instance>
            <instance>mtk_mnld2mtklogger</instance>
            <instance>mtk_mnld2nlputils</instance>
            <instance>mtk_mtklogger2mnld</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.log</name>
        <version>1.0</version>
        <interface>
            <name>ILog</name>
            <instance>ATMWiFiHidlServer</instance>
            <instance>ConnsysFWHidlServer</instance>
            <instance>LoggerHidlServer</instance>
            <instance>MobileLogHidlServer</instance>
            <instance>ModemLogHidlServer</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mdmonitor</name>
        <version>1.0</version>
        <interface>
            <name>IMDMonitorService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mmagent</name>
        <version>1.1</version>
        <interface>
            <name>IMMAgent</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mmlpq</name>
        <version>2</version>
        <interface>
            <name>IMmlpq</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mms</name>
        <version>1.6-7</version>
        <interface>
            <name>IMms</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkcodecservice</name>
        <version>1.1</version>
        <interface>
            <name>IMtkCodecService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkpower</name>
        <version>1.2</version>
        <interface>
            <name>IMtkPerf</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IMtkPower</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkpower_applist</name>
        <interface>
            <name>IMtkpower_applist</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex</name>
        <version>2.5</version>
        <interface>
            <name>IMtkRadioEx</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>mtkAssist1</instance>
            <instance>mtkAssist2</instance>
            <instance>mtkAssist3</instance>
            <instance>mtkAssist4</instance>
            <instance>mtkCap1</instance>
            <instance>mtkCap2</instance>
            <instance>mtkCap3</instance>
            <instance>mtkCap4</instance>
            <instance>mtkEm1</instance>
            <instance>mtkEm2</instance>
            <instance>mtkEm3</instance>
            <instance>mtkEm4</instance>
            <instance>mtkRcs1</instance>
            <instance>mtkRcs2</instance>
            <instance>mtkRcs3</instance>
            <instance>mtkRcs4</instance>
            <instance>mtkSe1</instance>
            <instance>mtkSe2</instance>
            <instance>mtkSe3</instance>
            <instance>mtkSe4</instance>
            <instance>mtkSlot1</instance>
            <instance>mtkSlot2</instance>
            <instance>mtkSlot3</instance>
            <instance>mtkSlot4</instance>
            <instance>mtkSmartRatSwitch1</instance>
            <instance>mtkSmartRatSwitch2</instance>
            <instance>mtkSmartRatSwitch3</instance>
            <instance>mtkSmartRatSwitch4</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex</name>
        <version>3.0</version>
        <interface>
            <name>IMtkRadioEx</name>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>imsSlot3</instance>
            <instance>imsSlot4</instance>
            <instance>mtkAssist1</instance>
            <instance>mtkAssist2</instance>
            <instance>mtkAssist3</instance>
            <instance>mtkAssist4</instance>
            <instance>mtkCap1</instance>
            <instance>mtkCap2</instance>
            <instance>mtkCap3</instance>
            <instance>mtkCap4</instance>
            <instance>mtkEm1</instance>
            <instance>mtkEm2</instance>
            <instance>mtkEm3</instance>
            <instance>mtkEm4</instance>
            <instance>mtkRcs1</instance>
            <instance>mtkRcs2</instance>
            <instance>mtkRcs3</instance>
            <instance>mtkRcs4</instance>
            <instance>mtkRsu1</instance>
            <instance>mtkRsu2</instance>
            <instance>mtkRsu3</instance>
            <instance>mtkRsu4</instance>
            <instance>mtkSe1</instance>
            <instance>mtkSe2</instance>
            <instance>mtkSe3</instance>
            <instance>mtkSe4</instance>
            <instance>mtkSlot1</instance>
            <instance>mtkSlot2</instance>
            <instance>mtkSlot3</instance>
            <instance>mtkSlot4</instance>
            <instance>mtkSmartRatSwitch1</instance>
            <instance>mtkSmartRatSwitch2</instance>
            <instance>mtkSmartRatSwitch3</instance>
            <instance>mtkSmartRatSwitch4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.data</name>
        <interface>
            <name>IMtkRadioExData</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.ims</name>
        <interface>
            <name>IMtkRadioExIms</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.messaging</name>
        <interface>
            <name>IMtkRadioExMessaging</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.modem</name>
        <interface>
            <name>IMtkRadioExModem</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.network</name>
        <interface>
            <name>IMtkRadioExNetwork</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.rcs</name>
        <interface>
            <name>IMtkRadioExRcs</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.sim</name>
        <interface>
            <name>IMtkRadioExSim</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex.voice</name>
        <interface>
            <name>IMtkRadioExVoice</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.netdagent</name>
        <version>1.0</version>
        <interface>
            <name>INetdagent</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.neuropilot.agent</name>
        <version>1.2</version>
        <interface>
            <name>IAgent</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.nvram</name>
        <version>1.1</version>
        <interface>
            <name>INvram</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.power</name>
        <version>2.1</version>
        <interface>
            <name>IPower</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.pplagent</name>
        <version>1.0</version>
        <interface>
            <name>IPplAgent</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.pq</name>
        <version>2.3-15</version>
        <interface>
            <name>IPictureQuality</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.presence</name>
        <version>1.0</version>
        <interface>
            <name>IPresence</name>
            <instance>presence_hal_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.radio_op</name>
        <version>2.0</version>
        <version>3.0</version>
        <interface>
            <name>IRadioOp</name>
            <instance>OpImsRILd1</instance>
            <instance>OpImsRILd2</instance>
            <instance>OpImsRILd3</instance>
            <instance>OpImsRILd4</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>slot3</instance>
            <instance>slot4</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.rcs</name>
        <version>2.0</version>
        <interface>
            <name>IRcs</name>
            <instance>rcs_hal_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.thp</name>
        <version>1.0</version>
        <interface>
            <name>ITHP</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.touchll</name>
        <version>1.0</version>
        <interface>
            <name>ITouchll</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.videotelephony</name>
        <version>1.0</version>
        <interface>
            <name>IVideoTelephony</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.vpu</name>
        <version>1.0</version>
        <interface>
            <name>Ivpu</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.wfo</name>
        <version>1.0</version>
        <interface>
            <name>IWifiOffload</name>
            <instance>wfo_hidl_service</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.wifi.supplicant</name>
        <version>2.0</version>
        <interface>
            <name>ISupplicant</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.wifi.supplicant</name>
        <version>2.2</version>
        <interface>
            <name>ISupplicant</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.nxp.hardware.nfc</name>
        <version>1.1</version>
        <interface>
            <name>INqNfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.nxp.nxpese</name>
        <version>1.0</version>
        <interface>
            <name>INxpEse</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.nxp.nxpnfc</name>
        <version>1.0</version>
        <interface>
            <name>INxpNfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.appradio</name>
        <version>1.0</version>
        <interface>
            <name>IOplusAppRadio</name>
            <instance>oplus_app_slot1</instance>
            <instance>oplus_app_slot2</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.binderstats</name>
        <version>1.0</version>
        <interface>
            <name>IBinderStats</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.biometrics.fingerprint</name>
        <version>2.0-1</version>
        <interface>
            <name>IBiometricsFingerprint</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.camera.slogan</name>
        <version>1.0</version>
        <interface>
            <name>ISlogan</name>
            <instance>internal/0</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.cameraextension</name>
        <version>1.0</version>
        <interface>
            <name>ICameraExtensionService</name>
            <instance>cameraextensionservice</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.cammidasservice</name>
        <version>1.0</version>
        <interface>
            <name>IMIDASService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.charger</name>
        <version>1.0</version>
        <interface>
            <name>ICharger</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.cryptoeng</name>
        <version>1.0</version>
        <interface>
            <name>ICryptoeng</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.cwb</name>
        <version>1.0</version>
        <interface>
            <name>ICwbService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.displaypanelfeature</name>
        <version>1.0</version>
        <interface>
            <name>IDisplayPanelFeature</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.eid</name>
        <version>1.0</version>
        <interface>
            <name>IEidDevice</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.engcamera</name>
        <version>1.0</version>
        <interface>
            <name>IEngCamera</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.engineer</name>
        <version>1.0</version>
        <interface>
            <name>IEngineer</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.extcamera</name>
        <version>1.0</version>
        <interface>
            <name>IExtCamera</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.felica</name>
        <version>1.0</version>
        <interface>
            <name>IFelicaDevice</name>
            <instance>oplusStor1</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.fido.fido2ca</name>
        <version>1.0</version>
        <interface>
            <name>IFidoDaemon</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.fido.fidoca</name>
        <version>1.0</version>
        <interface>
            <name>IFidoDaemon</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.gameopt</name>
        <version>1.0</version>
        <interface>
            <name>IGameOptHalService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.handlefactory</name>
        <version>1.0</version>
        <interface>
            <name>IHandleFactory</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.hdcp</name>
        <version>1.0-1</version>
        <interface>
            <name>IHdcp</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.ims</name>
        <interface>
            <name>IImsStable</name>
            <instance>OplusImsRadio0</instance>
            <instance>OplusImsRadio1</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.midasdev</name>
        <version>1.0</version>
        <interface>
            <name>IMidasDev</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.misc</name>
        <version>1.0</version>
        <interface>
            <name>IOplusMisc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.mmdisplayfeature</name>
        <version>1.0</version>
        <interface>
            <name>IMMDisplayFeature</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.mtkmodemaci</name>
        <version>1.0</version>
        <interface>
            <name>IMtkModemAci</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.nfc</name>
        <version>1.0</version>
        <interface>
            <name>IOplusNfc</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.nfc.esepower</name>
        <version>1.0</version>
        <interface>
            <name>IEsePower</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.olc</name>
        <version>2.0</version>
        <interface>
            <name>IOplusLogCore</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.oplusSensor</name>
        <version>1.0</version>
        <interface>
            <name>ISensorFeature</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.orms</name>
        <version>1.0</version>
        <interface>
            <name>IOrmsHalProxy</name>
            <instance>ORMS_HAL_PROXY</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.ormsHalService</name>
        <interface>
            <name>IOrmsAidlHalService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.performance</name>
        <version>1.0</version>
        <interface>
            <name>IPerformance</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.power.powermonitor</name>
        <version>1.0</version>
        <interface>
            <name>IPowerMonitor</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.radio</name>
        <interface>
            <name>IRadioStable</name>
            <instance>OplusRadio0</instance>
            <instance>OplusRadio1</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.rpmh</name>
        <version>2.0</version>
        <interface>
            <name>IRpmh</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.subsys_interface.subsys</name>
        <interface>
            <name>ISubsys</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.subsys_interface.subsys_radio</name>
        <interface>
            <name>ISubsysRadio</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.thermalmntcfg</name>
        <version>1.0</version>
        <interface>
            <name>IThermalMntCfg</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.touch</name>
        <version>1.0</version>
        <interface>
            <name>IOplusTouch</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.transfer</name>
        <version>1.0</version>
        <interface>
            <name>ITransfer</name>
            <instance>ATMWiFiHidlServer</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.transmessage</name>
        <version>1.0</version>
        <interface>
            <name>ITransmessge</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.virtual_device.audio</name>
        <version>1.0</version>
        <interface>
            <name>IStatusProvider</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.virtual_device.camera.manager</name>
        <version>1.0</version>
        <interface>
            <name>IVirtualCameraManager</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.wifi</name>
        <version>1.1</version>
        <interface>
            <name>IOplusWifiService</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.wifi.supplicant</name>
        <version>2.0</version>
        <interface>
            <name>IOplusSupplicant</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="aidl" optional="true">
        <name>vendor.oplus.hardware.wifi.supplicant</name>
        <interface>
            <name>IOplusSupplicant</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.pixelworks.hardware.display</name>
        <version>1.0-2</version>
        <interface>
            <name>IIris</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.pixelworks.hardware.feature</name>
        <version>1.0</version>
        <interface>
            <name>IIrisFeature</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.tesiai.hardware.hdcpconnection</name>
        <version>1.0</version>
        <interface>
            <name>IHDCPConnection</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.trustonic.tee</name>
        <version>1.1</version>
        <interface>
            <name>ITee</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.trustonic.tee.tui</name>
        <version>1.0</version>
        <interface>
            <name>ITui</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.zeku.hardware.explorer</name>
        <version>1.0</version>
        <interface>
            <name>IMmsProvider</name>
            <instance>mms/explorer</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkradioex</name>
        <transport>hwbinder</transport>
        <version>3.0</version>
        <interface>
            <name>IMtkRadioEx</name>
            <instance>mtkSlot1</instance>
            <instance>mtkSlot2</instance>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>mtkSe1</instance>
            <instance>mtkSe2</instance>
            <instance>mtkEm1</instance>
            <instance>mtkEm2</instance>
            <instance>mtkAssist1</instance>
            <instance>mtkAssist2</instance>
            <instance>mtkRcs1</instance>
            <instance>mtkRcs2</instance>
            <instance>mtkCap1</instance>
            <instance>mtkCap2</instance>
            <instance>mtkSmartRatSwitch1</instance>
            <instance>mtkSmartRatSwitch2</instance>
            <instance>mtkRsu1</instance>
            <instance>mtkRsu2</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.lineage.touch</name>
        <version>1.0</version>
        <interface>
            <name>ITouchscreenGesture</name>
            <instance>default</instance>
        </interface>
    </hal>
    <hal format="hidl" optional="true">
        <name>android.hardware.nfc</name>
        <version>1.0-2</version>
        <interface>
            <name>INfc</name>
            <instance>default</instance>
        </interface>
    </hal>
</compatibility-matrix>
