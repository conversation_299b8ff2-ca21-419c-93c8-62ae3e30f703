<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!--
<!DOCTYPE MediaCodecs [
<!ELEMENT MediaCodecs (Decoders,Encoders)>
<!ELEMENT Decoders (MediaCodec*)>
<!ELEMENT Encoders (MediaCodec*)>
<!ELEMENT MediaCodec (Type*,Quirk*)>
<!ATTLIST MediaCodec name CDATA #REQUIRED>
<!ATTLIST MediaCodec type CDATA>
<!ELEMENT Type EMPTY>
<!ATTLIST Type name CDATA #REQUIRED>
<!ELEMENT Quirk EMPTY>
<!ATTLIST Quirk name CDATA #REQUIRED>
]>

There's a simple and a complex syntax to declare the availability of a
media codec:

A codec that properly follows the OpenMax spec and therefore doesn't have any
quirks and that only supports a single content type can be declared like so:

    <MediaCodec name="OMX.foo.bar" type="something/interesting" />

If a codec has quirks OR supports multiple content types, the following syntax
can be used:

    <MediaCodec name="OMX.foo.bar" >
        <Type name="something/interesting" />
        <Type name="something/else" />
        ...
        <Quirk name="requires-allocate-on-input-ports" />
        <Quirk name="requires-allocate-on-output-ports" />
        <Quirk name="output-buffers-are-unreadable" />
    </MediaCodec>

Only the three quirks included above are recognized at this point:

"requires-allocate-on-input-ports"
    must be advertised if the component does not properly support specification
    of input buffers using the OMX_UseBuffer(...) API but instead requires
    OMX_AllocateBuffer to be used.

"requires-allocate-on-output-ports"
    must be advertised if the component does not properly support specification
    of output buffers using the OMX_UseBuffer(...) API but instead requires
    OMX_AllocateBuffer to be used.

"output-buffers-are-unreadable"
    must be advertised if the emitted output buffers of a decoder component
    are not readable, i.e. use a custom format even though abusing one of
    the official OMX colorspace constants.
    Clients of such decoders will not be able to access the decoded data,
    naturally making the component much less useful. The only use for
    a component with this quirk is to render the output to the screen.
    Audio decoders MUST NOT advertise this quirk.
    Video decoders that advertise this quirk must be accompanied by a
    corresponding color space converter for thumbnail extraction,
    matching surfaceflinger support that can render the custom format to
    a texture and possibly other code, so just DON'T USE THIS QUIRK.

    2012/07/13 config for MTK OMX Media Codecs, created by Morris Yang (mtk03147)
-->

<MediaCodecs>
    <Encoders>
        <!-- MTK codec -->
        <MediaCodec name="c2.mtk.mpeg4.encoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="158-348" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.h263.encoder" type="video/3gpp" update="true">
            <Limit name="measured-frame-rate-176x144" range="124-273" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.avc.encoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="66-145" />
            <Limit name="measured-frame-rate-720x480" range="142-312" />
            <Limit name="measured-frame-rate-1280x720" range="64-141" />
            <Limit name="measured-frame-rate-1920x1080" range="30-66" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.hevc.encoder" type="video/hevc" update="true">
            <Limit name="measured-frame-rate-320x240" range="98-216" />
            <Limit name="measured-frame-rate-720x480" range="40-88" />
            <Limit name="measured-frame-rate-1280x720" range="40-80" />
            <Limit name="measured-frame-rate-3840x2160" range="13-29" />
        </MediaCodec>
        <!-- Google codec -->
        <MediaCodec name="c2.android.avc.encoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="101-223" />
            <Limit name="measured-frame-rate-720x480" range="60-130" />
            <Limit name="measured-frame-rate-1280x720" range="21-46" />
            <Limit name="measured-frame-rate-1920x1080" range="13-29" />
        </MediaCodec>
        <MediaCodec name="c2.android.h263.encoder" type="video/3gpp" update="true">
            <Limit name="measured-frame-rate-176x144" range="275-605" />
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg4.encoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="263-579" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.encoder" type="video/x-vnd.on2.vp8" update="true">
            <Limit name="measured-frame-rate-320x180" range="140-308" />
            <Limit name="measured-frame-rate-640x360" range="69-151" />
            <Limit name="measured-frame-rate-1280x720" range="13-29" />
            <Limit name="measured-frame-rate-1920x1080" range="9-20" />
        </MediaCodec>
    </Encoders>
    <Decoders>
        <!-- MTK codec -->
        <MediaCodec name="c2.mtk.mpeg4.decoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="160-351" />
            <Limit name="measured-frame-rate-480x360" range="140-160" />
            <Limit name="measured-frame-rate-1280x720" range="115-130" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.h263.decoder" type="video/3gpp" update="true">
            <Limit name="measured-frame-rate-176x144" range="207-655" />
            <Limit name="measured-frame-rate-352x288" range="167-367" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.avc.decoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="105-232" />
            <Limit name="measured-frame-rate-720x480" range="324-713" />
            <Limit name="measured-frame-rate-1280x720" range="186-409" />
            <Limit name="measured-frame-rate-1920x1080" range="113-248" />
        </MediaCodec>
         <MediaCodec name="c2.mtk.hevc.decoder" type="video/hevc" update="true">
            <Limit name="measured-frame-rate-352x288" range="131-288" />
            <!-- #ifndef OPLUS_BUG_COMPATIBILITY-->
            <!-- <EMAIL>.2726993, 2021/12/07, Modify for CTS test-->
            <!--
            <Limit name="measured-frame-rate-640x360" range="285-626" />
            <Limit name="measured-frame-rate-720x480" range="145-155" />
            -->
            <!-- #else OPLUS_BUG_COMPATIBILITY-->
            <Limit name="measured-frame-rate-640x360" range="285-626" />
            <Limit name="measured-frame-rate-720x480" range="351-772" />
            <!-- #endif OPLUS_BUG_COMPATIBILITY-->
            <Limit name="measured-frame-rate-1280x720" range="206-454" />
            <Limit name="measured-frame-rate-1920x1080" range="125-276" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vpx.decoder" type="video/x-vnd.on2.vp8" update="true">
            <Limit name="measured-frame-rate-320x180" range="188-484" />
            <Limit name="measured-frame-rate-640x360" range="210-461" />
            <Limit name="measured-frame-rate-1280x720" range="56-123" />
            <Limit name="measured-frame-rate-1920x1080" range="23-51" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vp9.decoder" type="video/x-vnd.on2.vp9" update="true">
            <!-- #ifndef OPLUS_BUG_COMPATIBILITY-->
            <!-- <EMAIL>.2327311, 2021/10/21, Modify for CTS test-->
            <!--<Limit name="measured-frame-rate-320x180" range="425-936" />-->
            <!-- #else OPLUS_BUG_COMPATIBILITY-->
            <Limit name="measured-frame-rate-320x180" range="285-630" />
            <!-- #endif OPLUS_BUG_COMPATIBILITY-->
            <Limit name="measured-frame-rate-640x360" range="249-547" />
            <Limit name="measured-frame-rate-1280x720" range="139-305" />
            <Limit name="measured-frame-rate-1920x1080" range="114-251" />
        </MediaCodec>
        <!-- Google codec -->
        <MediaCodec name="c2.android.mpeg4.decoder" type="video/mp4v-es" update="true">
            <Limit name="measured-frame-rate-176x144" range="359-790" />
        </MediaCodec>
        <MediaCodec name="c2.android.h263.decoder" type="video/3gpp" update="true">
            <Limit name="measured-frame-rate-176x144" range="313-688" />
            <Limit name="measured-frame-rate-352x288" range="381-839" />
        </MediaCodec>
        <MediaCodec name="c2.android.avc.decoder" type="video/avc" update="true">
            <Limit name="measured-frame-rate-320x240" range="110-243" />
            <!-- #ifndef OPLUS_BUG_COMPATIBILITY-->
            <!-- <EMAIL>.3226509, 2022/3/3, Modify for CTS test-->
            <!--
            <Limit name="measured-frame-rate-720x480" range="31-68" />
            <Limit name="measured-frame-rate-1280x720" range="10-23" />
            <Limit name="measured-frame-rate-1920x1080" range="7-15" />
            -->
            <!-- #else OPLUS_BUG_COMPATIBILITY-->
            <Limit name="measured-frame-rate-720x480" range="51-114" />
            <Limit name="measured-frame-rate-1280x720" range="29-66" />
            <Limit name="measured-frame-rate-1920x1080" range="16-37" />
            <!-- #endif OPLUS_BUG_COMPATIBILITY-->
        </MediaCodec>
        <MediaCodec name="c2.android.hevc.decoder" type="video/hevc" update="true">
            <Limit name="measured-frame-rate-352x288" range="114-250" />
            <Limit name="measured-frame-rate-640x360" range="59-129" />
            <Limit name="measured-frame-rate-720x480" range="57-125" />
            <Limit name="measured-frame-rate-1280x720" range="21-46" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.decoder" type="video/x-vnd.on2.vp8" update="true">
            <Limit name="measured-frame-rate-320x180" range="188-413" />
            <Limit name="measured-frame-rate-640x360" range="195-428" />
            <Limit name="measured-frame-rate-1280x720" range="64-140" />
            <Limit name="measured-frame-rate-1920x1080" range="24-52" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp9.decoder" type="video/x-vnd.on2.vp9" update="true">
            <Limit name="measured-frame-rate-320x180" range="201-442" />
            <Limit name="measured-frame-rate-640x360" range="89-195" />
            <Limit name="measured-frame-rate-1280x720" range="52-115" />
            <Limit name="measured-frame-rate-1920x1080" range="29-64" />
        </MediaCodec>
    </Decoders>
</MediaCodecs>
