# Analyze SELinux Policy Duplicates and Create Fix Plan
# Reads the duplicates report and creates a systematic plan to fix them

param(
    [string]$ReportFile = "sepolicy_duplicates_report.json",
    [switch]$DryRun = $true
)

Write-Host "SELinux Policy Duplicate Analyzer" -ForegroundColor Green
Write-Host "Report File: $ReportFile" -ForegroundColor Yellow
Write-Host "Dry Run Mode: $DryRun" -ForegroundColor Yellow
Write-Host ""

if (-not (Test-Path $ReportFile)) {
    Write-Error "Report file not found: $ReportFile"
    exit 1
}

# Load duplicates report
$duplicates = Get-Content $ReportFile | ConvertFrom-Json

Write-Host "Loaded $($duplicates.Count) duplicate entries" -ForegroundColor Cyan
Write-Host ""

# Group duplicates by type and analyze
$exactNameDuplicates = $duplicates | Where-Object { $_.Type -eq "ExactName" }
$exactContentDuplicates = $duplicates | Where-Object { $_.Type -eq "ExactContent" }
$similarNameDuplicates = $duplicates | Where-Object { $_.Type -eq "SimilarName" }

Write-Host "=== ANALYSIS SUMMARY ===" -ForegroundColor Magenta
Write-Host "Exact Name Duplicates: $($exactNameDuplicates.Count)" -ForegroundColor Red
Write-Host "Exact Content Duplicates: $($exactContentDuplicates.Count)" -ForegroundColor Red
Write-Host "Similar Name Duplicates: $($similarNameDuplicates.Count)" -ForegroundColor DarkYellow
Write-Host ""

# Analyze exact name duplicates - these are the most critical
Write-Host "=== EXACT NAME DUPLICATES ANALYSIS ===" -ForegroundColor Red
$filesToRemove = @()
$filesToMerge = @()

foreach ($dup in $exactNameDuplicates) {
    $baseFile = $dup.BaseFile
    $compareFile = $dup.CompareFile
    $contentMatch = $dup.ContentMatch
    
    Write-Host "Duplicate: $($dup.CompareFile)" -ForegroundColor Yellow
    Write-Host "  Base (sepolicy_vndr): $baseFile (Size: $($dup.BaseSize))" -ForegroundColor Gray
    Write-Host "  Compare (sepolicy): $compareFile (Size: $($dup.CompareSize))" -ForegroundColor Gray
    Write-Host "  Content Match: $contentMatch" -ForegroundColor $(if($contentMatch) {"Green"} else {"Red"})
    
    # Decision logic
    if ($contentMatch) {
        # Identical content - safe to remove from sepolicy
        $filesToRemove += [PSCustomObject]@{
            File = $compareFile
            Reason = "Identical content to sepolicy_vndr/$baseFile"
            Action = "Remove"
            BaseFile = $baseFile
        }
        Write-Host "  DECISION: REMOVE (identical content)" -ForegroundColor Green
    } else {
        # Different content - needs manual review/merge
        $filesToMerge += [PSCustomObject]@{
            File = $compareFile
            Reason = "Different content from sepolicy_vndr/$baseFile"
            Action = "Manual Review/Merge"
            BaseFile = $baseFile
            BaseSize = $dup.BaseSize
            CompareSize = $dup.CompareSize
        }
        Write-Host "  DECISION: MANUAL REVIEW (different content)" -ForegroundColor DarkYellow
    }
    Write-Host ""
}

Write-Host "=== REMOVAL PLAN ===" -ForegroundColor Green
Write-Host "Files to remove (identical content): $($filesToRemove.Count)" -ForegroundColor Green
foreach ($file in $filesToRemove) {
    Write-Host "  - $($file.File)" -ForegroundColor Gray
    Write-Host "    Reason: $($file.Reason)" -ForegroundColor DarkGray
}

Write-Host ""
Write-Host "=== MANUAL REVIEW PLAN ===" -ForegroundColor DarkYellow
Write-Host "Files needing manual review: $($filesToMerge.Count)" -ForegroundColor DarkYellow
foreach ($file in $filesToMerge) {
    Write-Host "  - $($file.File)" -ForegroundColor Gray
    Write-Host "    Base: $($file.BaseFile) (Size: $($file.BaseSize))" -ForegroundColor DarkGray
    Write-Host "    Compare: $($file.File) (Size: $($file.CompareSize))" -ForegroundColor DarkGray
    Write-Host "    Reason: $($file.Reason)" -ForegroundColor DarkGray
}

# Create action plan
$actionPlan = @{
    ToRemove = $filesToRemove
    ToReview = $filesToMerge
    Summary = @{
        TotalDuplicates = $duplicates.Count
        ExactNameDuplicates = $exactNameDuplicates.Count
        FilesToRemove = $filesToRemove.Count
        FilesToReview = $filesToMerge.Count
    }
}

# Save action plan
$planFile = "duplicate_fix_plan.json"
$actionPlan | ConvertTo-Json -Depth 4 | Out-File -FilePath $planFile -Encoding UTF8
Write-Host ""
Write-Host "Action plan saved to: $planFile" -ForegroundColor Green

# Execute removal if not dry run
if (-not $DryRun -and $filesToRemove.Count -gt 0) {
    Write-Host ""
    Write-Host "=== EXECUTING REMOVALS ===" -ForegroundColor Red
    
    foreach ($file in $filesToRemove) {
        $filePath = $file.File
        if (Test-Path $filePath) {
            Write-Host "Removing: $filePath" -ForegroundColor Red
            Remove-Item -Path $filePath -Force
            Write-Host "  Removed successfully" -ForegroundColor Green
        } else {
            Write-Host "File not found: $filePath" -ForegroundColor DarkYellow
        }
    }
    
    Write-Host ""
    Write-Host "Removal complete. $($filesToRemove.Count) files removed." -ForegroundColor Green
} elseif ($filesToRemove.Count -gt 0) {
    Write-Host ""
    Write-Host "DRY RUN: Would remove $($filesToRemove.Count) files" -ForegroundColor DarkYellow
    Write-Host "Run with -DryRun:`$false to execute removals" -ForegroundColor DarkYellow
}

Write-Host ""
Write-Host "=== NEXT STEPS ===" -ForegroundColor Magenta
Write-Host "1. Review the files marked for manual review" -ForegroundColor White
Write-Host "2. For each file needing review, compare content and decide:" -ForegroundColor White
Write-Host "   - Keep sepolicy_vndr version (remove sepolicy version)" -ForegroundColor White
Write-Host "   - Merge content if both have unique valuable content" -ForegroundColor White
Write-Host "   - Update sepolicy_vndr if sepolicy version is more complete" -ForegroundColor White
Write-Host "3. Run this script with -DryRun:`$false to execute safe removals" -ForegroundColor White

return $actionPlan
