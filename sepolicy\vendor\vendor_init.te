allow vendor_init vendor_proc_decimal:file w_file_perms;
allow vendor_init nfc_data_vendor_file:dir { r_dir_perms create_dir_perms };

allow vendor_init proc_sched_stune:file w_file_perms;
allow vendor_init proc:file w_file_perms;
allow vendor_init vendor_sysfs_usb_supply:dir search;
allow vendor_init vendor_sysfs_usb_supply:file w_file_perms;

allow vendor_init vendor_sysfs_otg_switch:file w_file_perms;

allow vendor_init vendor_proc_display:file w_file_perms;

allow vendor_init vts_status_prop:file { read getattr open };
allow vendor_init system_prop:file { read getattr open };
allow vendor_init proc_swappiness:file rw_file_perms;
allow vendor_init prjname_file:file rw_file_perms;
# Allow vendor_init to set fingerprint properties
set_prop(vendor_init, vendor_fingerprint_prop)
