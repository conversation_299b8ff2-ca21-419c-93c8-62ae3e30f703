# SELinux Policy Duplicate Checker
# Compares sepolicy_vndr (readonly base) with sepolicy directory to find duplicates

param(
    [string]$BaseDir = "sepolicy_vndr",
    [string]$CompareDir = "sepolicy",
    [switch]$Detailed = $false
)

Write-Host "SELinux Policy Duplicate Checker" -ForegroundColor Green
Write-Host "Base Directory (readonly): $BaseDir" -ForegroundColor Yellow
Write-Host "Compare Directory: $CompareDir" -ForegroundColor Yellow
Write-Host ""

# Function to get file hash
function Get-FileHash-Safe {
    param([string]$FilePath)
    try {
        return (Get-FileHash -Path $FilePath -Algorithm SHA256).Hash
    }
    catch {
        return $null
    }
}

# Function to get all files recursively
function Get-AllFiles {
    param([string]$Directory)
    
    if (-not (Test-Path $Directory)) {
        Write-Warning "Directory not found: $Directory"
        return @()
    }
    
    return Get-ChildItem -Path $Directory -Recurse -File | ForEach-Object {
        [PSCustomObject]@{
            FullPath = $_.FullName
            RelativePath = $_.FullName.Substring((Resolve-Path $Directory).Path.Length + 1)
            Name = $_.Name
            BaseName = $_.BaseName
            Extension = $_.Extension
            Size = $_.Length
            Hash = Get-FileHash-Safe $_.FullName
            Directory = $_.DirectoryName
        }
    }
}

Write-Host "Scanning base directory: $BaseDir..." -ForegroundColor Cyan
$baseFiles = Get-AllFiles -Directory $BaseDir

Write-Host "Scanning compare directory: $CompareDir..." -ForegroundColor Cyan
$compareFiles = Get-AllFiles -Directory $CompareDir

Write-Host "Found $($baseFiles.Count) files in $BaseDir" -ForegroundColor Green
Write-Host "Found $($compareFiles.Count) files in $CompareDir" -ForegroundColor Green
Write-Host ""

# Find duplicates by different criteria
$duplicates = @{
    ExactName = @()
    ExactContent = @()
    SimilarName = @()
}

Write-Host "Checking for duplicates..." -ForegroundColor Cyan

# 1. Exact filename matches
foreach ($baseFile in $baseFiles) {
    $matches = $compareFiles | Where-Object { $_.Name -eq $baseFile.Name }
    foreach ($match in $matches) {
        $duplicates.ExactName += [PSCustomObject]@{
            Type = "ExactName"
            BaseFile = $baseFile.RelativePath
            CompareFile = $match.RelativePath
            BaseHash = $baseFile.Hash
            CompareHash = $match.Hash
            ContentMatch = ($baseFile.Hash -eq $match.Hash)
            BaseSize = $baseFile.Size
            CompareSize = $match.Size
        }
    }
}

# 2. Exact content matches (different names)
foreach ($baseFile in $baseFiles) {
    if ($baseFile.Hash) {
        $matches = $compareFiles | Where-Object { 
            $_.Hash -eq $baseFile.Hash -and $_.Name -ne $baseFile.Name 
        }
        foreach ($match in $matches) {
            $duplicates.ExactContent += [PSCustomObject]@{
                Type = "ExactContent"
                BaseFile = $baseFile.RelativePath
                CompareFile = $match.RelativePath
                BaseHash = $baseFile.Hash
                CompareHash = $match.Hash
                ContentMatch = $true
                BaseSize = $baseFile.Size
                CompareSize = $match.Size
            }
        }
    }
}

# 3. Similar names (same basename, different paths)
foreach ($baseFile in $baseFiles) {
    $matches = $compareFiles | Where-Object { 
        $_.BaseName -eq $baseFile.BaseName -and 
        $_.Extension -eq $baseFile.Extension -and 
        $_.Name -ne $baseFile.Name 
    }
    foreach ($match in $matches) {
        $duplicates.SimilarName += [PSCustomObject]@{
            Type = "SimilarName"
            BaseFile = $baseFile.RelativePath
            CompareFile = $match.RelativePath
            BaseHash = $baseFile.Hash
            CompareHash = $match.Hash
            ContentMatch = ($baseFile.Hash -eq $match.Hash)
            BaseSize = $baseFile.Size
            CompareSize = $match.Size
        }
    }
}

# Report results
Write-Host "=== DUPLICATE ANALYSIS RESULTS ===" -ForegroundColor Magenta
Write-Host ""

Write-Host "1. EXACT FILENAME MATCHES:" -ForegroundColor Red
if ($duplicates.ExactName.Count -eq 0) {
    Write-Host "   No exact filename matches found." -ForegroundColor Green
} else {
    foreach ($dup in $duplicates.ExactName) {
        Write-Host "   DUPLICATE FOUND:" -ForegroundColor Red
        Write-Host "   Base:    $($dup.BaseFile)" -ForegroundColor Yellow
        Write-Host "   Compare: $($dup.CompareFile)" -ForegroundColor Yellow
        Write-Host "   Content Match: $($dup.ContentMatch)" -ForegroundColor $(if($dup.ContentMatch) {"Red"} else {"DarkYellow"})
        Write-Host "   Sizes: $($dup.BaseSize) vs $($dup.CompareSize)" -ForegroundColor Gray
        Write-Host ""
    }
}

Write-Host "2. EXACT CONTENT MATCHES (different names):" -ForegroundColor Red
if ($duplicates.ExactContent.Count -eq 0) {
    Write-Host "   No exact content matches with different names found." -ForegroundColor Green
} else {
    foreach ($dup in $duplicates.ExactContent) {
        Write-Host "   CONTENT DUPLICATE:" -ForegroundColor Red
        Write-Host "   Base:    $($dup.BaseFile)" -ForegroundColor Yellow
        Write-Host "   Compare: $($dup.CompareFile)" -ForegroundColor Yellow
        Write-Host ""
    }
}

Write-Host "3. SIMILAR NAMES:" -ForegroundColor DarkYellow
if ($duplicates.SimilarName.Count -eq 0) {
    Write-Host "   No similar name matches found." -ForegroundColor Green
} else {
    foreach ($dup in $duplicates.SimilarName) {
        Write-Host "   SIMILAR NAME:" -ForegroundColor DarkYellow
        Write-Host "   Base:    $($dup.BaseFile)" -ForegroundColor Yellow
        Write-Host "   Compare: $($dup.CompareFile)" -ForegroundColor Yellow
        Write-Host "   Content Match: $($dup.ContentMatch)" -ForegroundColor $(if($dup.ContentMatch) {"Red"} else {"Orange"})
        Write-Host ""
    }
}

# Summary
$totalDuplicates = $duplicates.ExactName.Count + $duplicates.ExactContent.Count + $duplicates.SimilarName.Count
Write-Host "=== SUMMARY ===" -ForegroundColor Magenta
Write-Host "Total potential duplicates found: $totalDuplicates" -ForegroundColor $(if($totalDuplicates -gt 0) {"Red"} else {"Green"})
Write-Host "- Exact filename matches: $($duplicates.ExactName.Count)" -ForegroundColor Red
Write-Host "- Exact content matches: $($duplicates.ExactContent.Count)" -ForegroundColor Red  
Write-Host "- Similar name matches: $($duplicates.SimilarName.Count)" -ForegroundColor DarkYellow

# Export results for further processing
$allDuplicates = @()
$allDuplicates += $duplicates.ExactName
$allDuplicates += $duplicates.ExactContent
$allDuplicates += $duplicates.SimilarName

if ($allDuplicates.Count -gt 0) {
    $outputFile = "sepolicy_duplicates_report.json"
    $allDuplicates | ConvertTo-Json -Depth 3 | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host ""
    Write-Host "Detailed report saved to: $outputFile" -ForegroundColor Green
}

return $allDuplicates
