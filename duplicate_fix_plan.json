﻿{
    "Summary":  {
                    "TotalDuplicates":  170,
                    "ExactNameDuplicates":  170,
                    "FilesToRemove":  0,
                    "FilesToReview":  170
                },
    "ToReview":  [
                     {
                         "File":  "vendor\\device.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\device.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\device.te",
                         "BaseSize":  115,
                         "CompareSize":  189
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\file.te",
                         "BaseSize":  2371,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\file.te",
                         "BaseSize":  2371,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
                         "BaseSize":  1383,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
                         "BaseSize":  1383,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
                         "BaseSize":  3405,
                         "CompareSize":  96
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
                         "BaseSize":  3405,
                         "CompareSize":  10601
                     },
                     {
                         "File":  "vendor\\hwservice.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\hwservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\hwservice.te",
                         "BaseSize":  52,
                         "CompareSize":  496
                     },
                     {
                         "File":  "vendor\\hwservice_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\hwservice_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\hwservice_contexts",
                         "BaseSize":  71,
                         "CompareSize":  1892
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\mtk_hal_camera.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\mtk_hal_camera.te",
                         "BaseSize":  1150,
                         "CompareSize":  1139
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\property.te",
                         "BaseSize":  658,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\property.te",
                         "BaseSize":  658,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
                         "BaseSize":  559,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
                         "BaseSize":  559,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
                         "BaseSize":  231,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
                         "BaseSize":  231,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\system_server.te",
                         "BaseSize":  535,
                         "CompareSize":  481
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\non_plat\\vendor_init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\non_plat\\vendor_init.te",
                         "BaseSize":  202,
                         "CompareSize":  792
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
                         "BaseSize":  1595,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
                         "BaseSize":  1595,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\init.te",
                         "BaseSize":  43,
                         "CompareSize":  91
                     },
                     {
                         "File":  "vendor\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\init.te",
                         "BaseSize":  43,
                         "CompareSize":  1367
                     },
                     {
                         "File":  "private\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
                         "BaseSize":  194,
                         "CompareSize":  261
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
                         "BaseSize":  194,
                         "CompareSize":  97
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\property.te",
                         "BaseSize":  475,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\property.te",
                         "BaseSize":  475,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
                         "BaseSize":  336,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
                         "BaseSize":  336,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\radio.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\radio.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\radio.te",
                         "BaseSize":  215,
                         "CompareSize":  241
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_private\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_private\\system_server.te",
                         "BaseSize":  465,
                         "CompareSize":  481
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_public\\property.te",
                         "BaseSize":  106,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\debug\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\debug\\plat_public\\property.te",
                         "BaseSize":  106,
                         "CompareSize":  240
                     },
                     {
                         "File":  "vendor\\device.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\device.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\device.te",
                         "BaseSize":  11668,
                         "CompareSize":  189
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\file.te",
                         "BaseSize":  19900,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\file.te",
                         "BaseSize":  19900,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\file_contexts",
                         "BaseSize":  60474,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\file_contexts",
                         "BaseSize":  60474,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\genfs_contexts",
                         "BaseSize":  52559,
                         "CompareSize":  96
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\genfs_contexts",
                         "BaseSize":  52559,
                         "CompareSize":  10601
                     },
                     {
                         "File":  "vendor\\hal_audio_default.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hal_audio_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hal_audio_default.te",
                         "BaseSize":  7845,
                         "CompareSize":  1853
                     },
                     {
                         "File":  "vendor\\hal_graphics_allocator_default.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hal_graphics_allocator_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hal_graphics_allocator_default.te",
                         "BaseSize":  714,
                         "CompareSize":  184
                     },
                     {
                         "File":  "vendor\\hal_nfc_default.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hal_nfc_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hal_nfc_default.te",
                         "BaseSize":  146,
                         "CompareSize":  45
                     },
                     {
                         "File":  "vendor\\hal_power_default.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hal_power_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hal_power_default.te",
                         "BaseSize":  363,
                         "CompareSize":  2281
                     },
                     {
                         "File":  "vendor\\hwservice.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hwservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hwservice.te",
                         "BaseSize":  1992,
                         "CompareSize":  496
                     },
                     {
                         "File":  "vendor\\hwservice_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\hwservice_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\hwservice_contexts",
                         "BaseSize":  3450,
                         "CompareSize":  1892
                     },
                     {
                         "File":  "private\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\init.te",
                         "BaseSize":  4854,
                         "CompareSize":  91
                     },
                     {
                         "File":  "vendor\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\init.te",
                         "BaseSize":  4854,
                         "CompareSize":  1367
                     },
                     {
                         "File":  "private\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\kernel.te",
                         "BaseSize":  2863,
                         "CompareSize":  261
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\kernel.te",
                         "BaseSize":  2863,
                         "CompareSize":  97
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\mediaserver.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\mediaserver.te",
                         "BaseSize":  10111,
                         "CompareSize":  63
                     },
                     {
                         "File":  "vendor\\mtk_hal_c2.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\mtk_hal_c2.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\mtk_hal_c2.te",
                         "BaseSize":  2716,
                         "CompareSize":  237
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\mtk_hal_camera.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\mtk_hal_camera.te",
                         "BaseSize":  14489,
                         "CompareSize":  1139
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\priv_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\priv_app.te",
                         "BaseSize":  377,
                         "CompareSize":  344
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\property.te",
                         "BaseSize":  12982,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\property.te",
                         "BaseSize":  12982,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\property_contexts",
                         "BaseSize":  19275,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\property_contexts",
                         "BaseSize":  19275,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\radio.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\radio.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\radio.te",
                         "BaseSize":  1666,
                         "CompareSize":  241
                     },
                     {
                         "File":  "private\\service_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\service_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\service_contexts",
                         "BaseSize":  75,
                         "CompareSize":  270
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\surfaceflinger.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\surfaceflinger.te",
                         "BaseSize":  3243,
                         "CompareSize":  177
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\system_app.te",
                         "BaseSize":  1464,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\system_app.te",
                         "BaseSize":  1464,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\system_server.te",
                         "BaseSize":  8396,
                         "CompareSize":  481
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\untrusted_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\untrusted_app.te",
                         "BaseSize":  411,
                         "CompareSize":  183
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\vendor_init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\vendor_init.te",
                         "BaseSize":  6516,
                         "CompareSize":  792
                     },
                     {
                         "File":  "vendor\\vpud_native.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\non_plat\\vpud_native.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\non_plat\\vpud_native.te",
                         "BaseSize":  2052,
                         "CompareSize":  158
                     },
                     {
                         "File":  "vendor\\device.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\device.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\device.te",
                         "BaseSize":  178,
                         "CompareSize":  189
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\file.te",
                         "BaseSize":  1038,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\file.te",
                         "BaseSize":  1038,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\file_contexts",
                         "BaseSize":  1728,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\file_contexts",
                         "BaseSize":  1728,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\genfs_contexts",
                         "BaseSize":  1258,
                         "CompareSize":  96
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\genfs_contexts",
                         "BaseSize":  1258,
                         "CompareSize":  10601
                     },
                     {
                         "File":  "private\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\init.te",
                         "BaseSize":  569,
                         "CompareSize":  91
                     },
                     {
                         "File":  "vendor\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\init.te",
                         "BaseSize":  569,
                         "CompareSize":  1367
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\mediaserver.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\mediaserver.te",
                         "BaseSize":  253,
                         "CompareSize":  63
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\property.te",
                         "BaseSize":  3875,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\property.te",
                         "BaseSize":  3875,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\property_contexts",
                         "BaseSize":  3595,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\property_contexts",
                         "BaseSize":  3595,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\radio.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\radio.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\radio.te",
                         "BaseSize":  1328,
                         "CompareSize":  241
                     },
                     {
                         "File":  "private\\service_contexts",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\service_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\service_contexts",
                         "BaseSize":  297,
                         "CompareSize":  270
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\surfaceflinger.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\surfaceflinger.te",
                         "BaseSize":  483,
                         "CompareSize":  177
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\system_app.te",
                         "BaseSize":  762,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\system_app.te",
                         "BaseSize":  762,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\system_server.te",
                         "BaseSize":  1682,
                         "CompareSize":  481
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_private\\vendor_init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_private\\vendor_init.te",
                         "BaseSize":  99,
                         "CompareSize":  792
                     },
                     {
                         "File":  "vendor\\device.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\device.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\device.te",
                         "BaseSize":  149,
                         "CompareSize":  189
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\file.te",
                         "BaseSize":  429,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\file.te",
                         "BaseSize":  429,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\property.te",
                         "BaseSize":  910,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\property.te",
                         "BaseSize":  910,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "Reason":  "Different content from sepolicy_vndr/basic\\plat_public\\vtservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "basic\\plat_public\\vtservice.te",
                         "BaseSize":  300,
                         "CompareSize":  52
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\file.te",
                         "BaseSize":  337,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\file.te",
                         "BaseSize":  337,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\surfaceflinger.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\surfaceflinger.te",
                         "BaseSize":  51,
                         "CompareSize":  177
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
                         "BaseSize":  262,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
                         "BaseSize":  262,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\system_server.te",
                         "BaseSize":  229,
                         "CompareSize":  481
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\non_plat\\untrusted_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\non_plat\\untrusted_app.te",
                         "BaseSize":  342,
                         "CompareSize":  183
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\debug\\plat_private\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\debug\\plat_private\\system_server.te",
                         "BaseSize":  299,
                         "CompareSize":  481
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\file.te",
                         "BaseSize":  3194,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\file.te",
                         "BaseSize":  3194,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\file_contexts",
                         "BaseSize":  13989,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\file_contexts",
                         "BaseSize":  13989,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\genfs_contexts",
                         "BaseSize":  2941,
                         "CompareSize":  96
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\genfs_contexts",
                         "BaseSize":  2941,
                         "CompareSize":  10601
                     },
                     {
                         "File":  "vendor\\hal_fingerprint_default.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\hal_fingerprint_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\hal_fingerprint_default.te",
                         "BaseSize":  1275,
                         "CompareSize":  2229
                     },
                     {
                         "File":  "vendor\\hal_graphics_allocator_default.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\hal_graphics_allocator_default.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\hal_graphics_allocator_default.te",
                         "BaseSize":  223,
                         "CompareSize":  184
                     },
                     {
                         "File":  "vendor\\hwservice.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\hwservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\hwservice.te",
                         "BaseSize":  1962,
                         "CompareSize":  496
                     },
                     {
                         "File":  "vendor\\hwservice_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\hwservice_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\hwservice_contexts",
                         "BaseSize":  4335,
                         "CompareSize":  1892
                     },
                     {
                         "File":  "private\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\init.te",
                         "BaseSize":  1495,
                         "CompareSize":  91
                     },
                     {
                         "File":  "vendor\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\init.te",
                         "BaseSize":  1495,
                         "CompareSize":  1367
                     },
                     {
                         "File":  "private\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\kernel.te",
                         "BaseSize":  390,
                         "CompareSize":  261
                     },
                     {
                         "File":  "vendor\\kernel.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\kernel.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\kernel.te",
                         "BaseSize":  390,
                         "CompareSize":  97
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\mediaserver.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\mediaserver.te",
                         "BaseSize":  3277,
                         "CompareSize":  63
                     },
                     {
                         "File":  "vendor\\mtk_hal_c2.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\mtk_hal_c2.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\mtk_hal_c2.te",
                         "BaseSize":  420,
                         "CompareSize":  237
                     },
                     {
                         "File":  "vendor\\mtk_hal_camera.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\mtk_hal_camera.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\mtk_hal_camera.te",
                         "BaseSize":  3406,
                         "CompareSize":  1139
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\priv_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\priv_app.te",
                         "BaseSize":  255,
                         "CompareSize":  344
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\property.te",
                         "BaseSize":  13515,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\property.te",
                         "BaseSize":  13515,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\property_contexts",
                         "BaseSize":  16569,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\property_contexts",
                         "BaseSize":  16569,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\radio.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\radio.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\radio.te",
                         "BaseSize":  2969,
                         "CompareSize":  241
                     },
                     {
                         "File":  "private\\service_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\service_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\service_contexts",
                         "BaseSize":  510,
                         "CompareSize":  270
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\surfaceflinger.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\surfaceflinger.te",
                         "BaseSize":  3514,
                         "CompareSize":  177
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\system_app.te",
                         "BaseSize":  5687,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\system_app.te",
                         "BaseSize":  5687,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\system_server.te",
                         "BaseSize":  4221,
                         "CompareSize":  481
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\untrusted_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\untrusted_app.te",
                         "BaseSize":  1735,
                         "CompareSize":  183
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\vendor_init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\vendor_init.te",
                         "BaseSize":  3713,
                         "CompareSize":  792
                     },
                     {
                         "File":  "vendor\\vpud_native.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\vpud_native.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\vpud_native.te",
                         "BaseSize":  422,
                         "CompareSize":  158
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\non_plat\\vtservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\non_plat\\vtservice.te",
                         "BaseSize":  6152,
                         "CompareSize":  52
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\file.te",
                         "BaseSize":  2387,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\file.te",
                         "BaseSize":  2387,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\file_contexts",
                         "BaseSize":  1633,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\file_contexts",
                         "BaseSize":  1633,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "private\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\genfs_contexts",
                         "BaseSize":  15148,
                         "CompareSize":  96
                     },
                     {
                         "File":  "vendor\\genfs_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\genfs_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\genfs_contexts",
                         "BaseSize":  15148,
                         "CompareSize":  10601
                     },
                     {
                         "File":  "private\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\init.te",
                         "BaseSize":  324,
                         "CompareSize":  91
                     },
                     {
                         "File":  "vendor\\init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\init.te",
                         "BaseSize":  324,
                         "CompareSize":  1367
                     },
                     {
                         "File":  "private\\mediaserver.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\mediaserver.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\mediaserver.te",
                         "BaseSize":  765,
                         "CompareSize":  63
                     },
                     {
                         "File":  "private\\priv_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\priv_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\priv_app.te",
                         "BaseSize":  1135,
                         "CompareSize":  344
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\property.te",
                         "BaseSize":  8236,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\property.te",
                         "BaseSize":  8236,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\property_contexts",
                         "BaseSize":  8136,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\property_contexts",
                         "BaseSize":  8136,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "private\\radio.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\radio.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\radio.te",
                         "BaseSize":  5370,
                         "CompareSize":  241
                     },
                     {
                         "File":  "private\\service_contexts",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\service_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\service_contexts",
                         "BaseSize":  10194,
                         "CompareSize":  270
                     },
                     {
                         "File":  "vendor\\surfaceflinger.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\surfaceflinger.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\surfaceflinger.te",
                         "BaseSize":  1530,
                         "CompareSize":  177
                     },
                     {
                         "File":  "private\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\system_app.te",
                         "BaseSize":  3881,
                         "CompareSize":  165
                     },
                     {
                         "File":  "vendor\\system_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\system_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\system_app.te",
                         "BaseSize":  3881,
                         "CompareSize":  681
                     },
                     {
                         "File":  "vendor\\system_server.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\system_server.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\system_server.te",
                         "BaseSize":  5113,
                         "CompareSize":  481
                     },
                     {
                         "File":  "private\\untrusted_app.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\untrusted_app.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\untrusted_app.te",
                         "BaseSize":  432,
                         "CompareSize":  183
                     },
                     {
                         "File":  "vendor\\vendor_init.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\vendor_init.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\vendor_init.te",
                         "BaseSize":  244,
                         "CompareSize":  792
                     },
                     {
                         "File":  "private\\vtservice.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_private\\vtservice.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_private\\vtservice.te",
                         "BaseSize":  763,
                         "CompareSize":  52
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_public\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_public\\file.te",
                         "BaseSize":  342,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_public\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_public\\file.te",
                         "BaseSize":  342,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_public\\property.te",
                         "BaseSize":  859,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/bsp\\plat_public\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "bsp\\plat_public\\property.te",
                         "BaseSize":  859,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/legacy\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "legacy\\non_plat\\property_contexts",
                         "BaseSize":  424,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/legacy\\non_plat\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "legacy\\non_plat\\property_contexts",
                         "BaseSize":  424,
                         "CompareSize":  1165
                     },
                     {
                         "File":  "public\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/modem\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\file.te",
                         "BaseSize":  230,
                         "CompareSize":  68
                     },
                     {
                         "File":  "vendor\\file.te",
                         "Reason":  "Different content from sepolicy_vndr/modem\\file.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\file.te",
                         "BaseSize":  230,
                         "CompareSize":  1979
                     },
                     {
                         "File":  "private\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/modem\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\file_contexts",
                         "BaseSize":  1787,
                         "CompareSize":  246
                     },
                     {
                         "File":  "vendor\\file_contexts",
                         "Reason":  "Different content from sepolicy_vndr/modem\\file_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\file_contexts",
                         "BaseSize":  1787,
                         "CompareSize":  7603
                     },
                     {
                         "File":  "public\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/modem\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\property.te",
                         "BaseSize":  713,
                         "CompareSize":  273
                     },
                     {
                         "File":  "vendor\\property.te",
                         "Reason":  "Different content from sepolicy_vndr/modem\\property.te",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\property.te",
                         "BaseSize":  713,
                         "CompareSize":  240
                     },
                     {
                         "File":  "private\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/modem\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\property_contexts",
                         "BaseSize":  809,
                         "CompareSize":  3263
                     },
                     {
                         "File":  "vendor\\property_contexts",
                         "Reason":  "Different content from sepolicy_vndr/modem\\property_contexts",
                         "Action":  "Manual Review/Merge",
                         "BaseFile":  "modem\\property_contexts",
                         "BaseSize":  809,
                         "CompareSize":  1165
                     }
                 ],
    "ToRemove":  [

                 ]
}
