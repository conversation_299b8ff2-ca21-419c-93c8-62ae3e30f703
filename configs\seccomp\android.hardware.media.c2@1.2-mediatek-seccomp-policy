# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

futex: 1
# ioctl calls are filtered via the selinux policy.
ioctl: 1
sched_yield: 1
close: 1
dup: 1
ppoll: 1
mprotect: arg2 in ~PROT_EXEC || arg2 in ~PROT_WRITE
#mmap2: arg2 in ~PROT_EXEC || arg2 in ~PROT_WRITE
memfd_create: 1
ftruncate: 1
ftruncate64: 1

# mremap: Ensure |flags| are (MREMAP_MAYMOVE | MREMAP_FIXED) TODO: Once minijail
# parser support for '<' is in this needs to be modified to also prevent
# |old_address| and |new_address| from touching the exception vector page, which
# on ARM is statically loaded at 0xffff 0000. See
# http://infocenter.arm.com/help/index.jsp?topic=/com.arm.doc.ddi0211h/Babfeega.html
# for more details.
mremap: arg3 == 3 || arg3 == MREMAP_MAYMOVE
munmap: 1
prctl: 1
getuid32: 1
writev: 1
sigaltstack: 1
clone: 1
exit: 1
lseek: 1
rt_sigprocmask: 1
openat: 1
open: 1
fstat64: 1
write: 1
nanosleep: 1
setpriority: 1
set_tid_address: 1
getdents64: 1
readlinkat: 1
readlink: 1
read: 1
pread64: 1
fstatfs64: 1
gettimeofday: 1
faccessat: 1
_llseek: 1
fstatat64: 1
ugetrlimit: 1
exit_group: 1
restart_syscall: 1
rt_sigreturn: 1
getrandom: 1
madvise: 1
sched_setaffinity: 1
sched_getaffinity: 1
sched_getparam: 1
sched_getscheduler: 1
recvfrom: 1

# crash dump policy additions
sigreturn: 1
clock_gettime: 1
futex: 1
getpid: 1
gettid: 1
pipe2: 1
recvmsg: 1
process_vm_readv: 1
tgkill: 1
rt_sigaction: 1
rt_tgsigqueueinfo: 1
#prctl: arg0 == PR_GET_NO_NEW_PRIVS || arg0 == 0x53564d41
#mprotect: arg2 in 0x1|0x2
#mmap2: arg2 in 0x1|0x2
geteuid32: 1
getgid32: 1
getegid32: 1
getgroups32: 1
#ifdef OPLUS_FEATURE_VIDEO_LOG
#<EMAIL>, 2021/01/24, Add for Video Input Dump arm
unlinkat: 1
mkdirat: 1
flock: 1
#endif /*OPLUS_FEATURE_VIDEO_LOG*/