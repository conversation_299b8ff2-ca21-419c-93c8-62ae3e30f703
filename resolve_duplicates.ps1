# Resolve SELinux Policy Duplicates
# Strategy: Since sepolicy_vndr is readonly and contains comprehensive MediaTek policies,
# and sepolicy files are generally much smaller (likely stubs), we should remove the
# sepolicy duplicates to avoid conflicts.

param(
    [string]$PlanFile = "duplicate_fix_plan.json",
    [switch]$DryRun = $true,
    [switch]$Force = $false
)

Write-Host "SELinux Policy Duplicate Resolver" -ForegroundColor Green
Write-Host "Plan File: $PlanFile" -ForegroundColor Yellow
Write-Host "Dry Run Mode: $DryRun" -ForegroundColor Yellow
Write-Host ""

if (-not (Test-Path $PlanFile)) {
    Write-Error "Plan file not found: $PlanFile"
    exit 1
}

# Load the action plan
$plan = Get-Content $PlanFile | ConvertFrom-Json

Write-Host "=== ANALYSIS STRATEGY ===" -ForegroundColor Magenta
Write-Host "Based on file size analysis:" -ForegroundColor White
Write-Host "- sepolicy_vndr files are generally much larger (comprehensive MediaTek policies)" -ForegroundColor White
Write-Host "- sepolicy files are generally much smaller (likely minimal/stub versions)" -ForegroundColor White
Write-Host "- Strategy: Remove sepolicy duplicates to avoid conflicts with comprehensive sepolicy_vndr" -ForegroundColor White
Write-Host ""

# Analyze the files to review and categorize them
$filesToRemove = @()
$filesToBackup = @()
$criticalFiles = @()

foreach ($file in $plan.ToReview) {
    $baseSize = $file.BaseSize
    $compareSize = $file.CompareSize
    $compareFile = $file.File
    $baseFile = $file.BaseFile
    
    # Decision logic based on size difference
    $sizeRatio = if ($compareSize -gt 0) { $baseSize / $compareSize } else { [double]::MaxValue }
    
    if ($sizeRatio -gt 3) {
        # Base file is significantly larger - likely comprehensive vs stub
        $filesToRemove += [PSCustomObject]@{
            File = $compareFile
            BaseFile = $baseFile
            Reason = "Base file is $([math]::Round($sizeRatio, 1))x larger - removing smaller stub version"
            BaseSize = $baseSize
            CompareSize = $compareSize
            Action = "Remove"
        }
    } elseif ($sizeRatio -lt 0.5) {
        # Compare file is significantly larger - might need review
        $criticalFiles += [PSCustomObject]@{
            File = $compareFile
            BaseFile = $baseFile
            Reason = "Compare file is larger - needs manual review"
            BaseSize = $baseSize
            CompareSize = $compareSize
            Action = "Manual Review"
        }
    } else {
        # Similar sizes - backup and remove
        $filesToBackup += [PSCustomObject]@{
            File = $compareFile
            BaseFile = $baseFile
            Reason = "Similar sizes - backup before removal"
            BaseSize = $baseSize
            CompareSize = $compareSize
            Action = "Backup and Remove"
        }
    }
}

Write-Host "=== RESOLUTION PLAN ===" -ForegroundColor Green
Write-Host "Files to remove (base significantly larger): $($filesToRemove.Count)" -ForegroundColor Green
Write-Host "Files to backup and remove (similar sizes): $($filesToBackup.Count)" -ForegroundColor DarkYellow
Write-Host "Files needing manual review (compare larger): $($criticalFiles.Count)" -ForegroundColor Red
Write-Host ""

# Show critical files that need manual review
if ($criticalFiles.Count -gt 0) {
    Write-Host "=== CRITICAL FILES NEEDING MANUAL REVIEW ===" -ForegroundColor Red
    foreach ($file in $criticalFiles) {
        Write-Host "  $($file.File)" -ForegroundColor Red
        Write-Host "    Base: $($file.BaseFile) (Size: $($file.BaseSize))" -ForegroundColor Gray
        Write-Host "    Compare: $($file.File) (Size: $($file.CompareSize))" -ForegroundColor Gray
        Write-Host "    Reason: $($file.Reason)" -ForegroundColor Gray
        Write-Host ""
    }
}

# Create backup directory
$backupDir = "sepolicy_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

if (-not $DryRun) {
    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        Write-Host "Created backup directory: $backupDir" -ForegroundColor Green
    }
}

# Function to backup and remove file
function Backup-And-Remove {
    param($FilePath, $BackupDir, $DryRun)
    
    if (Test-Path $FilePath) {
        $relativePath = $FilePath
        $backupPath = Join-Path $BackupDir $relativePath
        $backupParent = Split-Path $backupPath -Parent
        
        if (-not $DryRun) {
            if (-not (Test-Path $backupParent)) {
                New-Item -ItemType Directory -Path $backupParent -Force | Out-Null
            }
            Copy-Item -Path $FilePath -Destination $backupPath -Force
            Remove-Item -Path $FilePath -Force
            Write-Host "  Backed up and removed: $FilePath" -ForegroundColor Green
        } else {
            Write-Host "  Would backup and remove: $FilePath" -ForegroundColor DarkYellow
        }
    } else {
        Write-Host "  File not found: $FilePath" -ForegroundColor DarkGray
    }
}

# Execute removal plan
if ($filesToRemove.Count -gt 0) {
    Write-Host "=== REMOVING STUB FILES ===" -ForegroundColor Green
    foreach ($file in $filesToRemove) {
        Write-Host "Removing: $($file.File)" -ForegroundColor Yellow
        Write-Host "  Reason: $($file.Reason)" -ForegroundColor Gray
        Backup-And-Remove -FilePath $file.File -BackupDir $backupDir -DryRun $DryRun
    }
}

if ($filesToBackup.Count -gt 0) {
    Write-Host ""
    Write-Host "=== BACKING UP AND REMOVING SIMILAR-SIZED FILES ===" -ForegroundColor DarkYellow
    foreach ($file in $filesToBackup) {
        Write-Host "Backing up and removing: $($file.File)" -ForegroundColor Yellow
        Write-Host "  Reason: $($file.Reason)" -ForegroundColor Gray
        Backup-And-Remove -FilePath $file.File -BackupDir $backupDir -DryRun $DryRun
    }
}

# Summary
$totalProcessed = $filesToRemove.Count + $filesToBackup.Count
Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Magenta
Write-Host "Total files processed: $totalProcessed" -ForegroundColor Green
Write-Host "Files removed (stubs): $($filesToRemove.Count)" -ForegroundColor Green
Write-Host "Files backed up and removed: $($filesToBackup.Count)" -ForegroundColor DarkYellow
Write-Host "Files needing manual review: $($criticalFiles.Count)" -ForegroundColor Red

if (-not $DryRun) {
    Write-Host "Backup location: $backupDir" -ForegroundColor Green
} else {
    Write-Host "DRY RUN: No files were actually modified" -ForegroundColor DarkYellow
    Write-Host "Run with -DryRun:`$false to execute the plan" -ForegroundColor DarkYellow
}

# Save updated plan
$resolutionPlan = @{
    ToRemove = $filesToRemove
    ToBackup = $filesToBackup
    CriticalReview = $criticalFiles
    Summary = @{
        TotalProcessed = $totalProcessed
        FilesToRemove = $filesToRemove.Count
        FilesToBackup = $filesToBackup.Count
        CriticalFiles = $criticalFiles.Count
        BackupDir = $backupDir
    }
}

$resolutionFile = "duplicate_resolution_plan.json"
$resolutionPlan | ConvertTo-Json -Depth 4 | Out-File -FilePath $resolutionFile -Encoding UTF8
Write-Host ""
Write-Host "Resolution plan saved to: $resolutionFile" -ForegroundColor Green

return $resolutionPlan
