# ==============================================
# Common SEPolicy Rule
# ==============================================

ctl.atcid-daemon-u                    u:object_r:system_mtk_ctl_atcid_daemon_u_prop:s0
persist.vendor.radio.port_index       u:object_r:system_mtk_atci_sys_prop:s0
vendor.ril.atci.flightmode            u:object_r:system_mtk_atci_sys_prop:s0
persist.vendor.service.atci.autostart u:object_r:system_mtk_atci_sys_prop:s0
persist.vendor.service.atci.usermode  u:object_r:system_mtk_atci_sys_prop:s0

vendor.ril.capctrl_loaded             u:object_r:system_mtk_capctrl_sys_prop:s0

persist.vendor.sys.aal. u:object_r:system_mtk_aal_prop:s0

vendor.moms.permission.control.policy.set u:object_r:system_mtk_permission_control_prop:s0

persist.vendor.ter u:object_r:system_mtk_terservice_prop:s0
vendor.ter.service u:object_r:system_mtk_terservice_prop:s0

ro.vendor.mtk_cta_set u:object_r:system_mtk_cta_set_prop:s0

ro.sys.current_rsc_path     u:object_r:system_mtk_rsc_sys_prop:s0
ro.product.current_rsc_path u:object_r:system_mtk_rsc_sys_prop:s0
ro.sys_ext.current_rsc_path u:object_r:system_mtk_rsc_sys_prop:s0

# Restrict access to starting/stopping campostalgo
ctl.start$camerapostalgo   u:object_r:system_mtk_ctl_campostalgo_prop:s0
ctl.stop$camerapostalgo    u:object_r:system_mtk_ctl_campostalgo_prop:s0
ctl.restart$camerapostalgo u:object_r:system_mtk_ctl_campostalgo_prop:s0

persist.vendor.radio.telecom.vibrate u:object_r:system_mtk_telecom_vibrate_prop:s0

ro.vendor.graphiclowlatency.version u:object_r:system_mtk_graphics_sf_gll_ro_prop:s0
vendor.debug.sf.                    u:object_r:system_mtk_debug_sf_prop:s0
vendor.debug.bq.                    u:object_r:system_mtk_debug_bq_prop:s0

# CT SelfRegister property
persist.vendor.radio.selfreg u:object_r:system_mtk_selfreg_prop:s0

# USB tethering property for auto test
persist.vendor.net.tethering u:object_r:system_mtk_usb_tethering_prop:s0

# android log much detect
persist.vendor.logmuch u:object_r:system_mtk_logmuch_prop:s0

persist.vendor.entitlement_enabled u:object_r:system_mtk_wfc_entitlement_prop:s0
persist.vendor.entitlement.sesurl  u:object_r:system_mtk_wfc_entitlement_prop:s0
persist.vendor.entitlement.dbg.    u:object_r:system_mtk_wfc_entitlement_prop:s0
persist.vendor.net.wo.epdg_fqdn    u:object_r:system_mtk_wfc_entitlement_prop:s0

persist.vendor.mtk_wfc_opt_in    u:object_r:system_mtk_wfc_opt_in_prop:s0
persist.vendor.opt-in.url        u:object_r:system_mtk_opt_in_url_prop:s0
persist.vendor.apptoken.required u:object_r:system_mtk_apptoken_required_prop:s0

# common data releated property
persist.vendor.radio.default.data.selected u:object_r:system_mtk_common_data_prop:s0
persist.vendor.radio.mobile.mtu            u:object_r:system_mtk_common_data_prop:s0
vendor.radio.dsda.state                    u:object_r:system_mtk_common_data_prop:s0

# carrier express (cxp)
persist.vendor.operator.optr_1 u:object_r:system_mtk_usp_srv_prop:s0
persist.vendor.operator.spec_1 u:object_r:system_mtk_usp_srv_prop:s0
persist.vendor.operator.seg_1  u:object_r:system_mtk_usp_srv_prop:s0
persist.vendor.mtk_usp         u:object_r:system_mtk_usp_srv_prop:s0

persist.vendor.update_finished u:object_r:system_mtk_update_prop:s0
persist.vendor.previous_slot   u:object_r:system_mtk_update_prop:s0

vendor.media.wfd.             u:object_r:system_mtk_media_wfd_prop:s0
vendor.media.wfd.portrait     u:object_r:system_mtk_media_wfd_prop:s0
vendor.media.wfd.video-format u:object_r:system_mtk_media_wfd_prop:s0

vendor.gsm.disable.sim.dialog u:object_r:system_mtk_vsim_sys_prop:s0

# supplementary service property
vendor.gsm.radio.ss.sc                u:object_r:system_mtk_supp_serv_prop:s0
vendor.gsm.radio.ss.imsdereg          u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.iccid.       u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.change.      u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu_over_ims     u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.sync_for_ota u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.mode         u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.timeslot.    u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.radio.cfu.querytype    u:object_r:system_mtk_supp_serv_prop:s0
persist.vendor.suppserv.              u:object_r:system_mtk_supp_serv_prop:s0
vendor.suppserv.                      u:object_r:system_mtk_supp_serv_prop:s0

vendor.bluetooth.         u:object_r:system_mtk_bluetooth_prop:s0
persist.vendor.bluetooth. u:object_r:system_mtk_bluetooth_prop:s0

# tel log property
persist.vendor.log.tel_dbg u:object_r:system_mtk_em_tel_log_prop:s0

# ims config property
vendor.ril.imsconfig.force.notify u:object_r:system_mtk_imsconfig_prop:s0

# mtk duraspeed property
persist.vendor.sys.vm.drop_caches u:object_r:system_mtk_duraspeed_drop_caches_prop:s0

ro.vendor.mtk_system_update_support u:object_r:system_mtk_update_support_prop:s0

# AMS dynamic enable log property
persist.vendor.sys.activitylog u:object_r:system_mtk_amslog_prop:s0

# AMS-aal dynamic enable property
persist.vendor.sys.mtk_app_aal_support u:object_r:system_mtk_amsaal_prop:s0

# MTK CDMA Less property
persist.vendor.vzw_device_type u:object_r:system_mtk_persist_vendor_vzw_device_type_prop:s0

persist.vendor.mtk_rtt_support u:object_r:system_mtk_rtt_prop:s0

persist.vendor.ctm_slot_flag u:object_r:system_mtk_ctmslot_prop:s0

persist.vendor.mtk_uce_support u:object_r:system_mtk_uce_support_prop:s0

persist.vendor.mtk_clientapi_support u:object_r:system_mtk_clientapi_support_prop:s0

vendor.cdma.icc.operator.mcc u:object_r:system_mtk_cdma_prop:s0

# ECBM property
vendor.ril.cdma.inecmmode_by_slot u:object_r:system_mtk_cdma_ecm_prop:s0

persist.vendor.mtk_rcs_support u:object_r:system_mtk_rcs_support_prop:s0

# MTK World Phone property
persist.vendor.radio.wm_selectmode u:object_r:system_mtk_world_phone_prop:s0
persist.vendor.radio.wm_fddtimer   u:object_r:system_mtk_world_phone_prop:s0

# MTK Capability Switch property
persist.vendor.radio.unlock           u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.unlock.roaming   u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.wait.imsi        u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.waitimsi.roaming u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.sim.status       u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.new.sim.slot     u:object_r:system_mtk_capability_switch_prop:s0
vendor.ril.imsi.status.               u:object_r:system_mtk_capability_switch_prop:s0
persist.vendor.radio.simswitchstate   u:object_r:system_mtk_capability_switch_prop:s0

persist.vendor.subsidylock.connectivity_status u:object_r:system_mtk_subsidylock_connect_prop:s0
persist.vendor.subsidylock                     u:object_r:system_mtk_subsidylock_prop:s0

persist.vendor.mtk_acs_version u:object_r:system_mtk_acs_version_prop:s0
persist.vendor.mtk_acs_support u:object_r:system_mtk_acs_support_prop:s0
persist.vendor.mtk_acs_url     u:object_r:system_mtk_acs_url_prop:s0

# Modem Monitor property===========
persist.vendor.mdmmonitor u:object_r:config_prop:s0

init.svc.mtk_pkm_service u:object_r:system_mtk_pkm_init_prop:s0

# MDM init control property
init.svc.md_monitor u:object_r:system_mtk_init_svc_md_monitor_prop:s0

# netflix HD property
ro.netflix.bsp_rev u:object_r:netflix_bsp_rev_prop:s0

service.ctm.slot_flag u:object_r:system_mtk_ctm_prop:s0

vendor.sf.gll.istarget u:object_r:system_mtk_graphics_sf_gll_prop:s0
vendor.sf.gll.q2l      u:object_r:system_mtk_graphics_sf_gll_prop:s0
vendor.sf.gll.avgl2p   u:object_r:system_mtk_graphics_sf_gll_prop:s0

ro.vendor.mtk_gwsd_support u:object_r:system_mtk_gwsd_prop:s0

ro.vendor.vodata_support u:object_r:system_mtk_vodata_prop:s0

# fastdormancy property
persist.vendor.fd.on.charge         u:object_r:system_mtk_fd_prop:s0
persist.vendor.fd.screen.off.only   u:object_r:system_mtk_fd_prop:s0

#allow entitlement to set vonr debug
persist.vendor.dbg.vonr_ui_ovr u:object_r:system_mtk_dbg_ims_prop:s0
