﻿# Camera
genfscon proc /boost_pool                                                                   u:object_r:proc_boost_pool:s0
# Charging
genfscon sysfs /devices/platform/charger/power_supply/ac                                    u:object_r:vendor_sysfs_ac_supply:s0
genfscon sysfs /devices/platform/charger/power_supply/battery                               u:object_r:vendor_sysfs_battery_supply:s0
genfscon sysfs /devices/platform/charger/power_supply/usb                                   u:object_r:vendor_sysfs_usb_supply:s0
genfscon proc /batt_param_noplug                                                            u:object_r:vendor_proc_batt_param:s0
genfscon proc /charger                                                                      u:object_r:vendor_proc_charger:s0
genfscon proc /charger_critical_log                                                         u:object_r:vendor_proc_charger:s0
genfscon proc /charger_cycle                                                                u:object_r:vendor_proc_charger:s0
genfscon proc /charger_log                                                                  u:object_r:vendor_proc_charger:s0
genfscon proc /devinfo/fastchg                                                              u:object_r:vendor_proc_devinfo_fastchg:s0
genfscon proc /fastchg_fw_update                                                            u:object_r:vendor_proc_fw_update:s0
genfscon proc /tbatt_pwroff                                                                 u:object_r:vendor_proc_tbatt_pwroff:s0
genfscon proc /ui_soc_decimal                                                               u:object_r:vendor_proc_decimal:s0
genfscon proc /wireless                                                                     u:object_r:vendor_proc_wireless:s0
genfscon sysfs /class/oplus_chg                                                             u:object_r:vendor_sysfs_usb_supply:s0
genfscon sysfs /devices/virtual/oplus_chg                                                   u:object_r:vendor_sysfs_usb_supply:s0
genfscon sysfs /devices/platform/extcon_usb/extcon                                          u:object_r:sysfs_extcon:s0
genfscon sysfs /devices/system/cpu/cpufreq/mtk/.cluster_(min|max)_freq                      u:object_r:sysfs_mtk_cpufreq:s0
genfscon sysfs /kernel/gpu/gpu_(min|max)_clock                                              u:object_r:sysfs_mtk_gpufreq:s0
# Display
genfscon proc /devinfo/lcd                                                                  u:object_r:vendor_proc_display:s0
genfscon proc /touchpanel                                                                   u:object_r:vendor_proc_display:s0
genfscon sysfs /kernel/oplus_display                                                        u:object_r:vendor_sysfs_graphics:s0
# Fingerprint
genfscon proc /fp_id                                                                        u:object_r:vendor_proc_fingerprint:s0
genfscon proc /fp_kernel_event                                                              u:object_r:vendor_proc_fingerprint:s0
# GPU
genfscon sysfs /devices/platform/13000000.mali                                              u:object_r:sysfs_gpu:s0
genfscon sysfs /kernel/gpu/gpu_min_clock                                                    u:object_r:vendor_sysfs_gpu:s0
genfscon sysfs /kernel/gpu/gpu_max_clock                                                    u:object_r:vendor_sysfs_gpu:s0
genfscon sysfs /devices/platform/13000000.mali/js_ctx_scheduling_mode                       u:object_r:vendor_sysfs_gpu:s0
genfscon sysfs /devices/platform/13000000.mali/js_scheduling_period                         u:object_r:vendor_sysfs_gpu:s0
genfscon sysfs /devices/platform/13000000.mali/dvfs_period                                  u:object_r:vendor_sysfs_gpu:s0
# NFC
genfscon proc /oplus_nfc/chipset                                                            u:object_r:vendor_proc_nfc_chipset:s0
# OTG
genfscon sysfs /devices/virtual/oplus_chg/usb/otg_switch                                    u:object_r:vendor_sysfs_otg_switch:s0
# Sensors
genfscon proc /oplusSensorFeature                                                           u:object_r:vendor_proc_oplus_sensor_feature:s0
genfscon proc /oplusCustom/Sensor                                                           u:object_r:vendor_proc_oplus_sensor_feature:s0
genfscon proc /mag_soft_parameter.json                                                      u:object_r:vendor_proc_oplus_sensor_param:s0
genfscon proc /sensor/als_cali                                                              u:object_r:vendor_proc_oplus_sensor_cali:s0
genfscon sysfs /devices/virtual/oplus_sensor                                                u:object_r:vendor_sysfs_oplus_virtual_sensor:s0
genfscon sysfs /class/oplus_sensor                                                          u:object_r:vendor_sysfs_oplus_virtual_sensor:s0
# Oplus
genfscon proc /oplusVersion                                                                 u:object_r:vendor_proc_oplus_version:s0
genfscon proc /oplusVersion/prjName                                                         u:object_r:prjname_file:s0
# Performance
genfscon proc /sys/kernel/sched_stune_task_threshold                                        u:object_r:proc_sched_stune:s0 
genfscon proc /sys/kernel/sched_assist_enabled                                              u:object_r:proc_sched_assist:s0
genfscon proc /sys/kernel/sched_assist_scene                                                u:object_r:proc_sched_assist:s0
# Wakeup
genfscon sysfs /devices/platform/usb0/wakeup/wakeup124                                      u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/charger/power_supply/battery/wakeup179                     u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/charger/power_supply/usb/wakeup178                         u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/charger/power_supply/ac/wakeup177                          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19031000.vpu_core1/wakeup/wakeup170                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-004e/wakeup/wakeup132                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/1a000000.camsys/wakeup/wakeup112                           u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19030000.vpu_core0/wakeup/wakeup169                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19030000.vpu_core1/wakeup/wakeup169                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11d04000.i2c8/i2c-8/8-0028/wakeup/wakeup139                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/odm/odm:oplus,track-charge/track/wakeup/wakeup145          u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19032000.vpu_core2/wakeup/wakeup170                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19030000.vpu_core0/wakeup/wakeup168                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-004e/wakeup/wakeup130                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/15020000.imgsys_config/wakeup/wakeup110                    u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11015000.i2c0/i2c-0/0-0038/wakeup/wakeup175                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-0028/wakeup/wakeup139                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/19032000.vpu_core2/wakeup/wakeup171                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11f00000.i2c5/i2c-5/5-004e/wakeup/wakeup131                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/10800000.adsp_core0/wakeup/wakeup13                        u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/15020000.imgsys_config/wakeup/wakeup111                    u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/11015000.i2c0/i2c-0/0-0038/wakeup/wakeup176                u:object_r:sysfs_wakeup:s0
genfscon sysfs /devices/platform/odm/odm:oplus,track-charge/track/wakeup/wakeup146          u:object_r:sysfs_wakeup:s0
# Vibrator - denniz
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/duration          u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/trigger           u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/state             u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/activate          u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/vmax              u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/brightness        u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/seq               u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb1000.i2c9/i2c-9/9-005a/leds/vibrator/loop              u:object_r:sysfs_vibrator:s0
# Vibrator - cupida
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/duration          u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/trigger           u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/state             u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/activate          u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/vmax              u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/brightness        u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/seq               u:object_r:sysfs_vibrator:s0
genfscon sysfs /devices/platform/11cb0000.i2c3/i2c-3/3-005a/leds/vibrator/loop              u:object_r:sysfs_vibrator:s0
# Swap
genfscon proc /sys/vm/direct_swappiness                                                     u:object_r:proc_swappiness:s0
# TEE
genfscon proc /tee_bind_core                                                                u:object_r:vendor_proc_fingerprint:s0
