# ==============================================
# Common SEPolicy Rule
# ==============================================

allow radio mtk_registry_service:service_manager add;

# Fix boot violation
add_service(radio, mtk_radio_service)

# Date : WK1721 2017/5/26
# Operation : IT
# Purpose : Allow to use HAL Wfo
hal_client_domain(radio, hal_mtk_wfo)

# Date : 2017/06/06
# Purpose: for iphonesubinfoEx service
add_service(radio, mtk_phonesubinfo_service)

# Date : 2017/06/15
# Purpose: for mtksimphonebook service
add_service(radio, mtk_simphonebook_service)

# Date : 2017/08/14
# Operation : VT development
# Purpose : Add vtservice to support video telephony functionality
#           3G VT/ViLTE both use this service which will also communication with IMCB/Rild
allow radio vtservice_service:service_manager find;

# Date : 2017/09/25
# Operation : Migration IT with Privacy Protection Lock
# Purpose : for pplSmsFilterExtension find ppl_agent service
allow radio ppl_agent_service:service_manager find;

# Date : 2018/06/22
# Operation : P migration
# Purpose : Allow ctm to set system_mtk_ctmslot_prop
set_prop(radio, system_mtk_ctmslot_prop)

# Date : WK18.26 2018/06/25
# Operation : IT
# Purpose : for setting ims config force notify property
set_prop(radio, system_mtk_imsconfig_prop)

# Date : 2018/06/28
# Operation : P migration
# Purpose : Allow radio to set vendor prop in core domain
set_prop(radio, system_mtk_cdma_prop)

# Date: 2018/06/29
# Operation: P migration
# Purpose : allow radio set world phohe property
set_prop(radio, system_mtk_world_phone_prop)

# Date : 2018/6/29
# Operation: P migration
set_prop(radio, system_mtk_vsim_sys_prop)
set_prop(radio, ctl_start_prop)
set_prop(radio, ctl_stop_prop)

# Date : 2020/10/30
# Operation: R migration
set_prop(radio, system_mtk_capctrl_sys_prop)

# Date : 2018/07/02
# Operation : Migration
# Purpose : Allow Phone process to set ECBM property
set_prop(radio, system_mtk_cdma_ecm_prop)

# Date : 2018/07/02
# Operation : P migration
# Purpose : Allow radio to get/set system_mtk_supp_serv_prop
set_prop(radio, system_mtk_supp_serv_prop)

# Date : 2018/07/03
# Operation : P migration
# Purpose : Allow framework to set system_mtk_common_data_prop
set_prop(radio, system_mtk_common_data_prop)

# Date: 2018/07/03
# Operation: P migration
# Purpose : allow radio set capability switch property
set_prop(radio, system_mtk_capability_switch_prop)

# Date: 2018/07/04
# Operation: P migration
# Purpose : allow radio get vzw device type property
get_prop(radio, system_mtk_persist_vendor_vzw_device_type_prop)

# Date : 2018/07/03
# Stage: Migration
# Purpose: allow radio to get RTT property
get_prop(radio, system_mtk_rtt_prop)

# Date: 2018/07/06
# Operation: Migration
# Purpose : Allow Entitlement to get system_mtk_wfc_entitlement_prop
# Package: com.mediatek.entitlement
set_prop(radio, system_mtk_wfc_entitlement_prop)

# Date: 2018/09/13
# Operation: Support UCE Property
set_prop(radio, system_mtk_uce_support_prop)

# Date : 2018/10/05
# Operation : P Migration
# Purpose : allow to find aal_service
allow radio aal_service:service_manager find;

# Date: 2018/10/31
# Operation: Support SubsidyLock
set_prop(radio, system_mtk_subsidylock_connect_prop)

# Date: 2018/10/31
# Operation: Support SubsidyLock
get_prop(radio, system_mtk_subsidylock_prop)

# Date: 2018/12/20
# Operation: Support GWSD
add_service(radio, mtk_gwsd_service)

# Date : 2019/05/16
# Operation : MDM IT with Swift app
# Purpose : for app labeled by radio to auto start md_monitor
set_prop(radio, config_prop)

# Date : 2019/05/28
# Operation : Q Migration
# Purpose : allow to get mtk_cta_set and mtk_cta_support property
get_prop(radio, system_mtk_cta_set_prop)

# Date : 2019/06/03
# Operation : Q Migration split build
# Purpose : allow to get system_mtk_rsc_sys_prop
get_prop(radio, system_mtk_rsc_sys_prop)

# Date: 2019/07/23
# Operation : Swift app IT
# Purpose: allow to read init.svc.md_monitor property for calling SystemService.waitForState()
get_prop(radio, system_mtk_init_svc_md_monitor_prop)

# Date : 2020/03/20
# Operation: R migration
get_prop(radio, system_mtk_telecom_vibrate_prop)

# Date : 2020/04/13
# Purpose: get CT Register system property
get_prop(radio, system_mtk_selfreg_prop)

# Date : 2020/03/23
# Operation : Migration
# Purpose : apps need to get networkdatacontroller Service
allow radio cta_networkdatacontroller_service:service_manager find;

# Date : 2021/07/27
# Operation : Migration
# Purpose : Allow presence service to add AOSP service by ServiceManager
allow radio uce_service:service_manager add;

# Date: 2019/04/10
# Purpose: ctm set property
# Package Name: cn.richinfo.dm
set_prop(radio, system_mtk_ctm_prop)

# Date: 2020/06/30
# Operation: Support VODATA
add_service(radio, mtk_vodata_service)

# Date : 2020/09/16
# Operation : R Migration
# Purpose : allow radio to get telephony log property
get_prop(radio, system_mtk_em_tel_log_prop)

# Date : 2021/01/07
# Purpose : Allow radio to read ro.vendor.mtk_gwsd_support
get_prop(radio, system_mtk_gwsd_prop)

# Date : 2021/04/25
# Purpose : Allow radio to read fastdormancy property
get_prop(radio, system_mtk_fd_prop)

# Date : 2021/12/22
# Purpose : Allow radio to read ims debug property
get_prop(radio, system_mtk_dbg_ims_prop)

# Allow radio to get system_mtk_vodata_prop
get_prop(radio, system_mtk_vodata_prop)
