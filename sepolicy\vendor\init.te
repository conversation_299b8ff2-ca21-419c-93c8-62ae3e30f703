﻿allow proc_perfmgr proc:filesystem associate ;
allow proc_cpufreq proc:filesystem associate ;
allow vendor_proc_display proc:filesystem associate ;
allow init vendor_shell_exec:file {r_file_perms execute};
allow init vendor_toolbox_exec:file {r_file_perms execute};
allow init proc:file rw_file_perms;
allow init proc_swappiness:file rw_file_perms;
allow init proc_watermark_scale_factor:file rw_file_perms;
allow init sysfs_devices_block:file rw_file_perms;
allow init sysfs_leds:file create_file_perms;
allow init mtk_hal_camera_exec:file {r_file_perms execute};
allow init vendor_sysfs_otg_switch:file create_file_perms;
allow init vendor_sysfs_usb_supply:file create_file_perms;
allow init vendor_sysfs_graphics:file create_file_perms;
allow init vendor_proc_display:file create_file_perms;
allow init vtservice_hidl:fd { use };
allow init shell_exec:file {r_file_perms execute};
allow init hal_audio_default:file rw_file_perms;
allow init system_file:file {r_file_perms execute};
allow init hal_performance_oplus_exec:file {r_file_perms execute};
allow init hal_performance_oplus_hwservice:hwservice_manager find;
allow init mtk_hal_videotelephony_hwservice:hwservice_manager find;
allow init oplus_block_device:lnk_file relabelto;
allow init sysfs_vibrator:file rw_file_perms;
get_prop(init, exported_system_prop)
