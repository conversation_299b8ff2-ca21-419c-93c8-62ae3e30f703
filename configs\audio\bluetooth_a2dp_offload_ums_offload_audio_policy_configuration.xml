<?xml version="1.0" encoding="UTF-8"?>
<!-- Bluetooth Audio HAL Audio Policy Configuration file -->
<module name="bluetooth" halVersion="2.0">
    <mixPorts>
        <!-- A2DP Audio Ports -->
        <mixPort name="a2dp output" role="source"/>
        <!-- Hearing AIDs Audio Ports -->
        <mixPort name="hearing aid output" role="source">
            <profile name="" format="AUDIO_FORMAT_PCM_16_BIT"
                     samplingRates="24000 16000"
                     channelMasks="AUDIO_CHANNEL_OUT_STEREO"/>
        </mixPort>
    </mixPorts>
    <devicePorts>
        <!-- A2DP Audio Ports -->
        <devicePort tagName="BT A2DP Out" type="AUDIO_DEVICE_OUT_BLUETOOTH_A2DP" role="sink" encodedFormats="AUDIO_FORMAT_LDAC AUDIO_FORMAT_LHDC AUDIO_FORMAT_LHDC_LL">
            <profile name="" format="AUDIO_FORMAT_PCM_16_BIT"
                     samplingRates="44100 48000 88200 96000"
                     channelMasks="AUDIO_CHANNEL_OUT_STEREO"/>
        </devicePort>
        <devicePort tagName="BT A2DP Headphones" type="AUDIO_DEVICE_OUT_BLUETOOTH_A2DP_HEADPHONES" role="sink" encodedFormats="AUDIO_FORMAT_LDAC AUDIO_FORMAT_LHDC AUDIO_FORMAT_LHDC_LL">
            <profile name="" format="AUDIO_FORMAT_PCM_16_BIT"
                     samplingRates="44100 48000 88200 96000"
                     channelMasks="AUDIO_CHANNEL_OUT_STEREO"/>
        </devicePort>
        <devicePort tagName="BT A2DP Speaker" type="AUDIO_DEVICE_OUT_BLUETOOTH_A2DP_SPEAKER" role="sink" encodedFormats="AUDIO_FORMAT_LDAC AUDIO_FORMAT_LHDC AUDIO_FORMAT_LHDC_LL">
            <profile name="" format="AUDIO_FORMAT_PCM_16_BIT"
                     samplingRates="44100 48000 88200 96000"
                     channelMasks="AUDIO_CHANNEL_OUT_STEREO"/>
        </devicePort>
        <!-- Hearing AIDs Audio Ports -->
        <devicePort tagName="BT Hearing Aid Out" type="AUDIO_DEVICE_OUT_HEARING_AID" role="sink"/>
    </devicePorts>
    <routes>
        <route type="mix" sink="BT A2DP Out"
               sources="a2dp output"/>
        <route type="mix" sink="BT A2DP Headphones"
               sources="a2dp output"/>
        <route type="mix" sink="BT A2DP Speaker"
               sources="a2dp output"/>
        <route type="mix" sink="BT Hearing Aid Out"
               sources="hearing aid output"/>
    </routes>
</module>
