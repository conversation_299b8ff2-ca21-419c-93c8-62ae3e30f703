type mtk_hal_c2, domain;
type mtk_hal_c2_exec, exec_type, vendor_file_type, file_type;

init_daemon_domain(mtk_hal_c2)

# can route /dev/binder traffic to /dev/vndbinder
vndbinder_use(mtk_hal_c2)

hal_server_domain(mtk_hal_c2, hal_codec2)

# mediacodec may use an input surface from a different Codec2 or OMX service
hal_client_domain(mtk_hal_c2, hal_codec2)

hal_client_domain(mtk_hal_c2, hal_allocator)
hal_client_domain(mtk_hal_c2, hal_graphics_allocator)

allow mtk_hal_c2 gpu_device:chr_file rw_file_perms;
allow mtk_hal_c2 ion_device:chr_file rw_file_perms;
allow mtk_hal_c2 video_device:chr_file rw_file_perms;
allow mtk_hal_c2 video_device:dir search;

crash_dump_fallback(mtk_hal_c2)

# mediacodec should never execute any executable without a domain transition
neverallow mtk_hal_c2 { file_type fs_type }:file execute_no_trans;

# Media processing code is inherently risky and thus should have limited
# permissions and be isolated from the rest of the system and network.
# Lengthier explanation here:
# https://android-developers.googleblog.com/2016/05/hardening-media-stack.html
neverallow mtk_hal_c2 domain:{ udp_socket rawip_socket } *;
neverallow mtk_hal_c2 { domain userdebug_or_eng(`-su') }:tcp_socket *;

#============= mtk_hal_c2 ==============
allow mtk_hal_c2 debugfs_ion:dir search;
allow mtk_hal_c2 proc_ged:file rw_file_perms;
allowxperm mtk_hal_c2 proc_ged:file ioctl { proc_ged_ioctls };
allow mtk_hal_c2 gpu_device:dir search;
allow mtk_hal_c2 mtk_cmdq_device:chr_file r_file_perms;
hal_client_domain(mtk_hal_c2, hal_mtk_pq)
allow mtk_hal_c2 vcodec_file:dir w_dir_perms;
allow mtk_hal_c2 vcodec_file:file create_file_perms;
allow mtk_hal_c2 mtk_mdp_device:chr_file r_file_perms;
allow mtk_hal_c2 mtk_mdp_sync_device:chr_file r_file_perms;
allow mtk_hal_c2 mtk_fmt_sync_device:chr_file r_file_perms_no_map;
allow mtk_hal_c2 mtk_fmt_device:chr_file rw_file_perms_no_map;
allow mtk_hal_c2 proc_meminfo:file r_file_perms;
dontaudit mtk_hal_c2 vcodec_file:file ioctl;

#============= mtk_hal_c2 for legacy vcodec ==============
allow mtk_hal_c2 tmpfs:dir search;
allow mtk_hal_c2 Vcodec_device:chr_file rw_file_perms;
allow mtk_hal_c2 MTK_SMI_device:chr_file r_file_perms;
allow mtk_hal_c2 sysfs_concurrency_scenario:file rw_file_perms;
allow mtk_hal_c2 sysfs_concurrency_scenario:dir search;

#============= mtk_hal_c2 for dmabuf heap ==============
allow mtk_hal_c2 dmabuf_system_heap_device:chr_file r_file_perms;
allow mtk_hal_c2 dmabuf_system_secure_heap_device:chr_file r_file_perms;

#============= mtk_hal_c2 for log properties ==============
get_prop(mtk_hal_c2, vendor_mtk_c2_log_prop)

#============= mtk_hal_c2 for power hal ==============
hal_client_domain(mtk_hal_c2, hal_power)
