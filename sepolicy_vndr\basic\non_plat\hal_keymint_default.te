# ==============================================
# Common SEPolicy Rule
# ==============================================

# Date : 2021/08/12
# Operation: Keymint 1.0
# Purpose: Access attestation key in persist partition.
allow hal_keymint_default mnt_vendor_file:dir search;
allow hal_keymint_default persist_data_file:dir search;
allow hal_keymint_default persist_data_file:file r_file_perms;

# Date : 2021/08/12
# Operation: Keymint 1.0
# Purpose : Open MobiCore access permission for keystore.
allow hal_keymint_default mobicore:unix_stream_socket { connectto read write };
allow hal_keymint_default mobicore_user_device:chr_file rw_file_perms;

set_prop(hal_keymint_default, vendor_mtk_soter_teei_prop)
