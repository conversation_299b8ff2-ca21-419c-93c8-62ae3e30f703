﻿[
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\device.te",
        "CompareFile":  "vendor\\device.te",
        "BaseHash":  "E57618217DE4A6696F5AB089F0D36DACB4DA639BEFECD90C284E311325E2B413",
        "CompareHash":  "F309757D40481FA828C2BD68AB064FDD5011C0AF8BD03A52D17930DAF036B717",
        "ContentMatch":  false,
        "BaseSize":  115,
        "CompareSize":  189
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "67830135F40D03EFB5A39692B6ED137CEC3693E66E811F1402C3F68E624D3926",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  2371,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "67830135F40D03EFB5A39692B6ED137CEC3693E66E811F1402C3F68E624D3926",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  2371,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "92992B5010F209B3D394709C98439740996187352289576AE5F692D68A083F6A",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  1383,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "92992B5010F209B3D394709C98439740996187352289576AE5F692D68A083F6A",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  1383,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
        "CompareFile":  "private\\genfs_contexts",
        "BaseHash":  "556E3F7238BF41AAF7F8AC86CD00A9C2C0A76743EA1484937E5E3943A3A1F407",
        "CompareHash":  "E1D01955A0F8688D38E1C2AD5CF53AD85CC73E4F8215FF50B6B5A20A391D6C7B",
        "ContentMatch":  false,
        "BaseSize":  3405,
        "CompareSize":  96
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\genfs_contexts",
        "CompareFile":  "vendor\\genfs_contexts",
        "BaseHash":  "556E3F7238BF41AAF7F8AC86CD00A9C2C0A76743EA1484937E5E3943A3A1F407",
        "CompareHash":  "FFFBB978D1843E2BD35A7D49C87CBDEB42FFCC38702EA3050F441F92311BA6BC",
        "ContentMatch":  false,
        "BaseSize":  3405,
        "CompareSize":  10601
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\hwservice.te",
        "CompareFile":  "vendor\\hwservice.te",
        "BaseHash":  "D69CDBB37010DCD11C92C84BEABDF4D67460E1BBC71E7D4C3A1F0676379AB652",
        "CompareHash":  "E8493219B12C2659753C13D0742967694CBA31B5BCE54F847DE635D741AC53A1",
        "ContentMatch":  false,
        "BaseSize":  52,
        "CompareSize":  496
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\hwservice_contexts",
        "CompareFile":  "vendor\\hwservice_contexts",
        "BaseHash":  "150ADCFB55D3BDFB9C6874085F0B60DEEB39F418643D672F08B1CCB41F79245C",
        "CompareHash":  "4CCB6872F519D906A568AC8BADD2991146A0B8150BED8A0C2ECC15611C6B25BD",
        "ContentMatch":  false,
        "BaseSize":  71,
        "CompareSize":  1892
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\mtk_hal_camera.te",
        "CompareFile":  "vendor\\mtk_hal_camera.te",
        "BaseHash":  "1138DDD958EF0CE0027E16FC7FFC5D34E76CC0710289D52828F78D24BD322210",
        "CompareHash":  "3EE5F9AC2037B9ABF671317BE2C7B2BD02E264D97249CECDC335C26099C2301E",
        "ContentMatch":  false,
        "BaseSize":  1150,
        "CompareSize":  1139
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "E94FC6867A4FADC21FE6A6DD007564984926214C080C52A5AC8B7E7E5ADA2F1E",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  658,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "E94FC6867A4FADC21FE6A6DD007564984926214C080C52A5AC8B7E7E5ADA2F1E",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  658,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "35D8493D88EA51A60A864872D0AF058C914D1A158E1A428D5D4B2EB1766AE1DA",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  559,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "35D8493D88EA51A60A864872D0AF058C914D1A158E1A428D5D4B2EB1766AE1DA",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  559,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "D3F12A1C0954A207CA4D6E3D234ABA6B9A621FEAFBCB954483536D476AB42C80",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  231,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "D3F12A1C0954A207CA4D6E3D234ABA6B9A621FEAFBCB954483536D476AB42C80",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  231,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "A3CDB7D03213C0F07BCD2D1138A94FB7B01C689BA362BC4DEBE91505551125DD",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  535,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\non_plat\\vendor_init.te",
        "CompareFile":  "vendor\\vendor_init.te",
        "BaseHash":  "8158476178A731C92A73DF6321169D5F61F2C6C07EED20087F349990A8B26580",
        "CompareHash":  "BE1F927E63441C8B0BA5F2A26AF01D3C691EC241085152C0F924D4473143A4D5",
        "ContentMatch":  false,
        "BaseSize":  202,
        "CompareSize":  792
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "A177F69656EE676B4C5B161CD9129F71802C3600CD6025AB0AD990A07CFFC35A",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  1595,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "A177F69656EE676B4C5B161CD9129F71802C3600CD6025AB0AD990A07CFFC35A",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  1595,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\init.te",
        "CompareFile":  "private\\init.te",
        "BaseHash":  "E1CF67143C9083D6023E6A7706F608A087A1C62B9061FCE272EEC043BAAA9D24",
        "CompareHash":  "6778484267A1C801297B98C7FF02ABAF37BB4B7347856D0A683AED07AB386A98",
        "ContentMatch":  false,
        "BaseSize":  43,
        "CompareSize":  91
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\init.te",
        "CompareFile":  "vendor\\init.te",
        "BaseHash":  "E1CF67143C9083D6023E6A7706F608A087A1C62B9061FCE272EEC043BAAA9D24",
        "CompareHash":  "F13AA6701B1ADA0068E677995ABA2F9CD7B45A8B590D1940B09C57E6D6A5BC81",
        "ContentMatch":  false,
        "BaseSize":  43,
        "CompareSize":  1367
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
        "CompareFile":  "private\\kernel.te",
        "BaseHash":  "E1B483840FAF9F28619E504C2C3066439412ED95F44FA847A3735BA9286C6BD8",
        "CompareHash":  "8892BF4F61DEC79010E9006C46BF61EA15AB988B67323924FAF598072780C737",
        "ContentMatch":  false,
        "BaseSize":  194,
        "CompareSize":  261
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\kernel.te",
        "CompareFile":  "vendor\\kernel.te",
        "BaseHash":  "E1B483840FAF9F28619E504C2C3066439412ED95F44FA847A3735BA9286C6BD8",
        "CompareHash":  "868768EDC93CE0F2F9E1DF6B15D09829A75BC9905746568D6EA28A863A8C2B8E",
        "ContentMatch":  false,
        "BaseSize":  194,
        "CompareSize":  97
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "243DED29431648F1EC596BDC7FB4C3E18FA4D1DE2416E7FDA529928F9A26E9FE",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  475,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "243DED29431648F1EC596BDC7FB4C3E18FA4D1DE2416E7FDA529928F9A26E9FE",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  475,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "2944C0687F155D96BB744CB333FC14814B28F449A603F88ACCAFEEBB1500A9F6",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  336,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "2944C0687F155D96BB744CB333FC14814B28F449A603F88ACCAFEEBB1500A9F6",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  336,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\radio.te",
        "CompareFile":  "private\\radio.te",
        "BaseHash":  "1BA462050903A5AB2BA0EF33BDC123A9770165A89346ACA52D140502EC1F8543",
        "CompareHash":  "9404F45EA63DE877A334D4DA7D29354BF0CE30F850C0F8940E1AA6AFDCD618D6",
        "ContentMatch":  false,
        "BaseSize":  215,
        "CompareSize":  241
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_private\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "BC2DF3069B47FE33394A5F4C28C09692F6652DF3E8BF7D2DED79CAA782C21AC9",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  465,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_public\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "9DCAFD5E23B321CC94D0DF1A94C20F109571C5462A8D278C08CCB86B053D6AA9",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  106,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\debug\\plat_public\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "9DCAFD5E23B321CC94D0DF1A94C20F109571C5462A8D278C08CCB86B053D6AA9",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  106,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\device.te",
        "CompareFile":  "vendor\\device.te",
        "BaseHash":  "277E87ABAFB524DE66B9F045F987288405657D584E9284D40223367CDC28E9DB",
        "CompareHash":  "F309757D40481FA828C2BD68AB064FDD5011C0AF8BD03A52D17930DAF036B717",
        "ContentMatch":  false,
        "BaseSize":  11668,
        "CompareSize":  189
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "6F62F2B2766423D56445AF105A6609BB11F901C6ADAF5688EF2BE4D8B4537733",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  19900,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "6F62F2B2766423D56445AF105A6609BB11F901C6ADAF5688EF2BE4D8B4537733",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  19900,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "FE935AE112748FFFA1180DD37DD33BB2DF42CF8D8AAA06C9C2D573B7CFD5109F",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  60474,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "FE935AE112748FFFA1180DD37DD33BB2DF42CF8D8AAA06C9C2D573B7CFD5109F",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  60474,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\genfs_contexts",
        "CompareFile":  "private\\genfs_contexts",
        "BaseHash":  "09BB5DE2766635A36F0E46E189753641D434DBF1117AD8A406A7FB84BB1DD3BE",
        "CompareHash":  "E1D01955A0F8688D38E1C2AD5CF53AD85CC73E4F8215FF50B6B5A20A391D6C7B",
        "ContentMatch":  false,
        "BaseSize":  52559,
        "CompareSize":  96
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\genfs_contexts",
        "CompareFile":  "vendor\\genfs_contexts",
        "BaseHash":  "09BB5DE2766635A36F0E46E189753641D434DBF1117AD8A406A7FB84BB1DD3BE",
        "CompareHash":  "FFFBB978D1843E2BD35A7D49C87CBDEB42FFCC38702EA3050F441F92311BA6BC",
        "ContentMatch":  false,
        "BaseSize":  52559,
        "CompareSize":  10601
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hal_audio_default.te",
        "CompareFile":  "vendor\\hal_audio_default.te",
        "BaseHash":  "965F467ED05C02E21C9E19E9A41129E271ACB9F0D571A7A2835049F723549C9E",
        "CompareHash":  "3E6D75F2A6DED2E2FB2537DEC319B48126F6E203D20894CD4FAB16A56EAC57D9",
        "ContentMatch":  false,
        "BaseSize":  7845,
        "CompareSize":  1853
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hal_graphics_allocator_default.te",
        "CompareFile":  "vendor\\hal_graphics_allocator_default.te",
        "BaseHash":  "874EACC3931DCB7ED817BA675DC6F776D07EB0E4F6048DC455514B5952B5B56B",
        "CompareHash":  "A7423023AB7A04841C483013F71E696A43DB0E5AE8872DB9AA86DE2FF2B4349E",
        "ContentMatch":  false,
        "BaseSize":  714,
        "CompareSize":  184
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hal_nfc_default.te",
        "CompareFile":  "vendor\\hal_nfc_default.te",
        "BaseHash":  "E5FEC8C5AA1BFC4BAC6FB53593100005B416DCA96A617060BC0A0570E32E4A45",
        "CompareHash":  "0403B79E2DDED305C3B0AE0368DB722004C0F174F5297C517FE76E7C16A3D29A",
        "ContentMatch":  false,
        "BaseSize":  146,
        "CompareSize":  45
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hal_power_default.te",
        "CompareFile":  "vendor\\hal_power_default.te",
        "BaseHash":  "030995DC5C62CE3FBE3F3E37EAB9D4CBDC88EDA428AFDDAAA8FF987671329096",
        "CompareHash":  "579DD0A072B90688518B6B8D922F347A601D893FA6036392C7937A1469C3B1C5",
        "ContentMatch":  false,
        "BaseSize":  363,
        "CompareSize":  2281
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hwservice.te",
        "CompareFile":  "vendor\\hwservice.te",
        "BaseHash":  "290D0945194F41584280CB7678F150B249EEA9EB13AC57E8DEB3651CF0F7AABD",
        "CompareHash":  "E8493219B12C2659753C13D0742967694CBA31B5BCE54F847DE635D741AC53A1",
        "ContentMatch":  false,
        "BaseSize":  1992,
        "CompareSize":  496
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\hwservice_contexts",
        "CompareFile":  "vendor\\hwservice_contexts",
        "BaseHash":  "457644BDBB5B34A07F3B3301CCEB3253C0FA88569D190D91C26A3275CB77F0CF",
        "CompareHash":  "4CCB6872F519D906A568AC8BADD2991146A0B8150BED8A0C2ECC15611C6B25BD",
        "ContentMatch":  false,
        "BaseSize":  3450,
        "CompareSize":  1892
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\init.te",
        "CompareFile":  "private\\init.te",
        "BaseHash":  "F001B320E45E2635E713CF1AA04399002D5A29DC7E28408D28151E318BC9219D",
        "CompareHash":  "6778484267A1C801297B98C7FF02ABAF37BB4B7347856D0A683AED07AB386A98",
        "ContentMatch":  false,
        "BaseSize":  4854,
        "CompareSize":  91
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\init.te",
        "CompareFile":  "vendor\\init.te",
        "BaseHash":  "F001B320E45E2635E713CF1AA04399002D5A29DC7E28408D28151E318BC9219D",
        "CompareHash":  "F13AA6701B1ADA0068E677995ABA2F9CD7B45A8B590D1940B09C57E6D6A5BC81",
        "ContentMatch":  false,
        "BaseSize":  4854,
        "CompareSize":  1367
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\kernel.te",
        "CompareFile":  "private\\kernel.te",
        "BaseHash":  "52AB3C0A3A582B1F5091CD450ED221D00C269075A01A2C2030816167342FEA3D",
        "CompareHash":  "8892BF4F61DEC79010E9006C46BF61EA15AB988B67323924FAF598072780C737",
        "ContentMatch":  false,
        "BaseSize":  2863,
        "CompareSize":  261
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\kernel.te",
        "CompareFile":  "vendor\\kernel.te",
        "BaseHash":  "52AB3C0A3A582B1F5091CD450ED221D00C269075A01A2C2030816167342FEA3D",
        "CompareHash":  "868768EDC93CE0F2F9E1DF6B15D09829A75BC9905746568D6EA28A863A8C2B8E",
        "ContentMatch":  false,
        "BaseSize":  2863,
        "CompareSize":  97
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\mediaserver.te",
        "CompareFile":  "private\\mediaserver.te",
        "BaseHash":  "3A92F4FD01046E16A75C503A2AC2628B8BB0ACE310B7197E4DCA934A0150C8FE",
        "CompareHash":  "4C8D36F7A07C41713ACBE85C026CBEF0F36DC2BEBABFEEA8C1D1586B9DE67AD9",
        "ContentMatch":  false,
        "BaseSize":  10111,
        "CompareSize":  63
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\mtk_hal_c2.te",
        "CompareFile":  "vendor\\mtk_hal_c2.te",
        "BaseHash":  "22CF1C063DCA2BF6B9D727D8F1CAF44FB073309FB18754AF2382369BE10EE3F3",
        "CompareHash":  "77A70BDF887F4A45B0078B898DD02A4AA7156632733FECB9E8E377B725220BCD",
        "ContentMatch":  false,
        "BaseSize":  2716,
        "CompareSize":  237
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\mtk_hal_camera.te",
        "CompareFile":  "vendor\\mtk_hal_camera.te",
        "BaseHash":  "7208C8DD44C251CB8145E47A386EBE6C3F9E9027A5D29605978BDE58263074A1",
        "CompareHash":  "3EE5F9AC2037B9ABF671317BE2C7B2BD02E264D97249CECDC335C26099C2301E",
        "ContentMatch":  false,
        "BaseSize":  14489,
        "CompareSize":  1139
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\priv_app.te",
        "CompareFile":  "private\\priv_app.te",
        "BaseHash":  "4AED6F015C55F07D2BC2FF8F74490737DE1C8A0C9A4CB811DC372C8988037E8C",
        "CompareHash":  "36A025DB4FBB7BB2905F76F68B543BAF23FA954CEBE31B6AD282529D3FA6CB3D",
        "ContentMatch":  false,
        "BaseSize":  377,
        "CompareSize":  344
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "41F4E794B310D232ABCACF2334723E2532DDA5717BDD6F11508BE8DD1E9D7F7F",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  12982,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "41F4E794B310D232ABCACF2334723E2532DDA5717BDD6F11508BE8DD1E9D7F7F",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  12982,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "C6B3C3930D950039FCD6875F78A032ED50FBE84FC2D228D519B9BF9C5DD1E15F",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  19275,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "C6B3C3930D950039FCD6875F78A032ED50FBE84FC2D228D519B9BF9C5DD1E15F",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  19275,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\radio.te",
        "CompareFile":  "private\\radio.te",
        "BaseHash":  "854C991D20455C0148FE15AFAAED07298D548D35FB011186B8277C9EE606D341",
        "CompareHash":  "9404F45EA63DE877A334D4DA7D29354BF0CE30F850C0F8940E1AA6AFDCD618D6",
        "ContentMatch":  false,
        "BaseSize":  1666,
        "CompareSize":  241
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\service_contexts",
        "CompareFile":  "private\\service_contexts",
        "BaseHash":  "14D99449853D6DDF1542E3BB90C1D803A0211C1B60E0195D62D9F78E6AA644C8",
        "CompareHash":  "223D6C812690FCD897D32919A26F036CBCE3A06FD6986FF4C99945312D4F3A39",
        "ContentMatch":  false,
        "BaseSize":  75,
        "CompareSize":  270
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\surfaceflinger.te",
        "CompareFile":  "vendor\\surfaceflinger.te",
        "BaseHash":  "9FB97E5D136534B813D816E15E4B0B1CAE0B8B3B45176F8FA9ED007CC157DA25",
        "CompareHash":  "AF01EED496D7E94C70420E4806B02D92B3B58224DA9650FFF104656524E72A7C",
        "ContentMatch":  false,
        "BaseSize":  3243,
        "CompareSize":  177
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "7F73DC2B8003AF64BF14AF21AB2F32AA969007FF545B59A035C3187EC2EC1C71",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  1464,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "7F73DC2B8003AF64BF14AF21AB2F32AA969007FF545B59A035C3187EC2EC1C71",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  1464,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "DE0F07514A43C41070FB5221B0EDE64F4A9051363E0116B0494E058863C0DD34",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  8396,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\untrusted_app.te",
        "CompareFile":  "private\\untrusted_app.te",
        "BaseHash":  "7DCC596F3CCAE4F6A234AC5A4EDD105F7DE6A5636DE5102F570C4D19CF9BA024",
        "CompareHash":  "6603A82BA3FAC19F9F2CAE40031BB8BF3B99EBF372984BA07E6ED296167F905A",
        "ContentMatch":  false,
        "BaseSize":  411,
        "CompareSize":  183
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\vendor_init.te",
        "CompareFile":  "vendor\\vendor_init.te",
        "BaseHash":  "94A934C8B063A4B61827E66BAECFFC1423C8D7871EDF9A7D232EA7A019D181F8",
        "CompareHash":  "BE1F927E63441C8B0BA5F2A26AF01D3C691EC241085152C0F924D4473143A4D5",
        "ContentMatch":  false,
        "BaseSize":  6516,
        "CompareSize":  792
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\non_plat\\vpud_native.te",
        "CompareFile":  "vendor\\vpud_native.te",
        "BaseHash":  "561943E7F8CD4999DACF771C81DC30C0787349870E13FC9163C69F9DF9159BDC",
        "CompareHash":  "807A0BFAD1E5B0079D25EA6D7CCFB4652A3DDE6797029C883303DAB663BB4667",
        "ContentMatch":  false,
        "BaseSize":  2052,
        "CompareSize":  158
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\device.te",
        "CompareFile":  "vendor\\device.te",
        "BaseHash":  "659E5AF373731A9485C27BD451DAC4B95324FB712739E6015642FB506898AAE9",
        "CompareHash":  "F309757D40481FA828C2BD68AB064FDD5011C0AF8BD03A52D17930DAF036B717",
        "ContentMatch":  false,
        "BaseSize":  178,
        "CompareSize":  189
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "5DF6B6663F05FB23EE3FA0DE49A9EC53D40A3051F99ADF16A45EC5024790D4C9",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  1038,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "5DF6B6663F05FB23EE3FA0DE49A9EC53D40A3051F99ADF16A45EC5024790D4C9",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  1038,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "9D4906F4952168ADAB5A2C3EA425EC8422C7EAF0E275B7121CE0680AA0DC23E1",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  1728,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "9D4906F4952168ADAB5A2C3EA425EC8422C7EAF0E275B7121CE0680AA0DC23E1",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  1728,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\genfs_contexts",
        "CompareFile":  "private\\genfs_contexts",
        "BaseHash":  "CA7B02B503BBA7010DCD454DABAFB6C0240269B5F5E22AA89B61B664B3C46F84",
        "CompareHash":  "E1D01955A0F8688D38E1C2AD5CF53AD85CC73E4F8215FF50B6B5A20A391D6C7B",
        "ContentMatch":  false,
        "BaseSize":  1258,
        "CompareSize":  96
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\genfs_contexts",
        "CompareFile":  "vendor\\genfs_contexts",
        "BaseHash":  "CA7B02B503BBA7010DCD454DABAFB6C0240269B5F5E22AA89B61B664B3C46F84",
        "CompareHash":  "FFFBB978D1843E2BD35A7D49C87CBDEB42FFCC38702EA3050F441F92311BA6BC",
        "ContentMatch":  false,
        "BaseSize":  1258,
        "CompareSize":  10601
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\init.te",
        "CompareFile":  "private\\init.te",
        "BaseHash":  "97B0CFC2340B8AEADDB0E3575636BE50C78CB4A0AFD57CDDD40EB1A0BDB97E1D",
        "CompareHash":  "6778484267A1C801297B98C7FF02ABAF37BB4B7347856D0A683AED07AB386A98",
        "ContentMatch":  false,
        "BaseSize":  569,
        "CompareSize":  91
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\init.te",
        "CompareFile":  "vendor\\init.te",
        "BaseHash":  "97B0CFC2340B8AEADDB0E3575636BE50C78CB4A0AFD57CDDD40EB1A0BDB97E1D",
        "CompareHash":  "F13AA6701B1ADA0068E677995ABA2F9CD7B45A8B590D1940B09C57E6D6A5BC81",
        "ContentMatch":  false,
        "BaseSize":  569,
        "CompareSize":  1367
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\mediaserver.te",
        "CompareFile":  "private\\mediaserver.te",
        "BaseHash":  "7FA6719B5BC658A34601DAF7513846D0E08BAE9039B789D8BC3CC1FA6B030553",
        "CompareHash":  "4C8D36F7A07C41713ACBE85C026CBEF0F36DC2BEBABFEEA8C1D1586B9DE67AD9",
        "ContentMatch":  false,
        "BaseSize":  253,
        "CompareSize":  63
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "E9DDC0D75C3258A4C63B0218BAA3130DCB6FE5BC9B26BF20D5B309B8F8835FC1",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  3875,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "E9DDC0D75C3258A4C63B0218BAA3130DCB6FE5BC9B26BF20D5B309B8F8835FC1",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  3875,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "86BB3A2C0AA44EA75AE63887C81C5F24E634D0C2BB11360F8299721FC7AAFD3F",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  3595,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "86BB3A2C0AA44EA75AE63887C81C5F24E634D0C2BB11360F8299721FC7AAFD3F",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  3595,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\radio.te",
        "CompareFile":  "private\\radio.te",
        "BaseHash":  "114D8EB0F9E4C07539983593CE38D60171CE35F040A51C222A8A4E22CA0A92F4",
        "CompareHash":  "9404F45EA63DE877A334D4DA7D29354BF0CE30F850C0F8940E1AA6AFDCD618D6",
        "ContentMatch":  false,
        "BaseSize":  1328,
        "CompareSize":  241
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\service_contexts",
        "CompareFile":  "private\\service_contexts",
        "BaseHash":  "E14E5D6A6FD7B88FEB87BC76A0059D5DC2DD68A18C31AEAD73A0D821652C5233",
        "CompareHash":  "223D6C812690FCD897D32919A26F036CBCE3A06FD6986FF4C99945312D4F3A39",
        "ContentMatch":  false,
        "BaseSize":  297,
        "CompareSize":  270
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\surfaceflinger.te",
        "CompareFile":  "vendor\\surfaceflinger.te",
        "BaseHash":  "FD0B958BF2B5B43BD4A321C8689D894EF06752C290C21B8A8EC29E0F9D7F8475",
        "CompareHash":  "AF01EED496D7E94C70420E4806B02D92B3B58224DA9650FFF104656524E72A7C",
        "ContentMatch":  false,
        "BaseSize":  483,
        "CompareSize":  177
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "4F93A002CC330B7F4014C8C17159DE08EEB6E8D0FF22C72DF70371869809F165",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  762,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "4F93A002CC330B7F4014C8C17159DE08EEB6E8D0FF22C72DF70371869809F165",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  762,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "55F6E36C03819A7F3BD228E95C451516E2DD71D27EA19776282CEECAE27000E5",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  1682,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_private\\vendor_init.te",
        "CompareFile":  "vendor\\vendor_init.te",
        "BaseHash":  "59DB132529D484BD8B2CE483FE990C60145C32F28729B6CF327C2336EBDB8768",
        "CompareHash":  "BE1F927E63441C8B0BA5F2A26AF01D3C691EC241085152C0F924D4473143A4D5",
        "ContentMatch":  false,
        "BaseSize":  99,
        "CompareSize":  792
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\device.te",
        "CompareFile":  "vendor\\device.te",
        "BaseHash":  "F8FEBCDB1AD434D00A003A8DDDB68D3DE936917FC234B441E5FB068D88CA4FFD",
        "CompareHash":  "F309757D40481FA828C2BD68AB064FDD5011C0AF8BD03A52D17930DAF036B717",
        "ContentMatch":  false,
        "BaseSize":  149,
        "CompareSize":  189
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "AC13153FAA4CCCC3C0B35630F4D099D2360728E88832CA761C2919686BA28C46",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  429,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "AC13153FAA4CCCC3C0B35630F4D099D2360728E88832CA761C2919686BA28C46",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  429,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "4B6AFA8FD4A22F0B921204336B777CF829FCDB68FB3E97412EB80D1827424200",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  910,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "4B6AFA8FD4A22F0B921204336B777CF829FCDB68FB3E97412EB80D1827424200",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  910,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "basic\\plat_public\\vtservice.te",
        "CompareFile":  "private\\vtservice.te",
        "BaseHash":  "ACFE9B5ECC44B330E5B54F57AEFA31FD76E203D5F52B31003D22C8B6E6064BF3",
        "CompareHash":  "41DDAC95AF3EE154BB0F9A7CF02CD478FD9A22E0883E309F495936BF58C381B8",
        "ContentMatch":  false,
        "BaseSize":  300,
        "CompareSize":  52
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "77821D8BB147872FE0AFD3122B13A6276B1ABC52FA0BEDCDB3C6CE8F618CB2EE",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  337,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "77821D8BB147872FE0AFD3122B13A6276B1ABC52FA0BEDCDB3C6CE8F618CB2EE",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  337,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\surfaceflinger.te",
        "CompareFile":  "vendor\\surfaceflinger.te",
        "BaseHash":  "FBDC351715B7CBD54EF5B997A3CB0B9DC19156B2815C4D1E5407330FEE2FC1C4",
        "CompareHash":  "AF01EED496D7E94C70420E4806B02D92B3B58224DA9650FFF104656524E72A7C",
        "ContentMatch":  false,
        "BaseSize":  51,
        "CompareSize":  177
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "7F67665E46E1B1E7B902D68F5646C218D28C3444549774ACBC46C30731ABC8CA",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  262,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "7F67665E46E1B1E7B902D68F5646C218D28C3444549774ACBC46C30731ABC8CA",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  262,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "4C4A1A6BB9EC9639047882DA5CF7DB2860D13B1BABD6179F6A51F3E6E2A24FD1",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  229,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\non_plat\\untrusted_app.te",
        "CompareFile":  "private\\untrusted_app.te",
        "BaseHash":  "3E05D3592721DA514758FF7EFFB45F533D26CD9B8E8271C6F199BD24B2F70700",
        "CompareHash":  "6603A82BA3FAC19F9F2CAE40031BB8BF3B99EBF372984BA07E6ED296167F905A",
        "ContentMatch":  false,
        "BaseSize":  342,
        "CompareSize":  183
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\debug\\plat_private\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "F235CB9D6532C8C55C1D232E96953803E58CF77FC5E321C374C2C3E353BDBAF5",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  299,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "B24627BC900305A19B6C5436D6421C2276F49BE9EF574981C60C567EAC357605",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  3194,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "B24627BC900305A19B6C5436D6421C2276F49BE9EF574981C60C567EAC357605",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  3194,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "97DA8B1E47F7D8382F41AB899C76FA3CF01E13C2DB9AAE737C45CEAC5ACC30F2",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  13989,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "97DA8B1E47F7D8382F41AB899C76FA3CF01E13C2DB9AAE737C45CEAC5ACC30F2",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  13989,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\genfs_contexts",
        "CompareFile":  "private\\genfs_contexts",
        "BaseHash":  "5B0524CA6E6D0CE3A295A8FD887AD445EE62A3ADF8D2C5B132492C9DE986943E",
        "CompareHash":  "E1D01955A0F8688D38E1C2AD5CF53AD85CC73E4F8215FF50B6B5A20A391D6C7B",
        "ContentMatch":  false,
        "BaseSize":  2941,
        "CompareSize":  96
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\genfs_contexts",
        "CompareFile":  "vendor\\genfs_contexts",
        "BaseHash":  "5B0524CA6E6D0CE3A295A8FD887AD445EE62A3ADF8D2C5B132492C9DE986943E",
        "CompareHash":  "FFFBB978D1843E2BD35A7D49C87CBDEB42FFCC38702EA3050F441F92311BA6BC",
        "ContentMatch":  false,
        "BaseSize":  2941,
        "CompareSize":  10601
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\hal_fingerprint_default.te",
        "CompareFile":  "vendor\\hal_fingerprint_default.te",
        "BaseHash":  "6C72B7D7E13F3FEB430BCDC4937FF6842F4DF912BC4E7CCE44CE2C19B05D7718",
        "CompareHash":  "138841C3B683DF98465C5E67A09722B82B5C38DE251524A67964B00BC31A421A",
        "ContentMatch":  false,
        "BaseSize":  1275,
        "CompareSize":  2229
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\hal_graphics_allocator_default.te",
        "CompareFile":  "vendor\\hal_graphics_allocator_default.te",
        "BaseHash":  "7BF231AD58D031C0A9F2532CDD363A9347E6495FEBADC7F69DD193334117A34F",
        "CompareHash":  "A7423023AB7A04841C483013F71E696A43DB0E5AE8872DB9AA86DE2FF2B4349E",
        "ContentMatch":  false,
        "BaseSize":  223,
        "CompareSize":  184
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\hwservice.te",
        "CompareFile":  "vendor\\hwservice.te",
        "BaseHash":  "1D9829BBCCCD79D02903D4EE7225FFC178C7DA051FDC9C48222DB97DD2954CBE",
        "CompareHash":  "E8493219B12C2659753C13D0742967694CBA31B5BCE54F847DE635D741AC53A1",
        "ContentMatch":  false,
        "BaseSize":  1962,
        "CompareSize":  496
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\hwservice_contexts",
        "CompareFile":  "vendor\\hwservice_contexts",
        "BaseHash":  "BC24AA2A5919785941235673CA0AA2E492C71CC45040782AE3BEA4A803E5ABEE",
        "CompareHash":  "4CCB6872F519D906A568AC8BADD2991146A0B8150BED8A0C2ECC15611C6B25BD",
        "ContentMatch":  false,
        "BaseSize":  4335,
        "CompareSize":  1892
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\init.te",
        "CompareFile":  "private\\init.te",
        "BaseHash":  "31411ECAFF918E28B33D5FCAF4D01D502F20E72B0BAFAA9C892C9165553910CB",
        "CompareHash":  "6778484267A1C801297B98C7FF02ABAF37BB4B7347856D0A683AED07AB386A98",
        "ContentMatch":  false,
        "BaseSize":  1495,
        "CompareSize":  91
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\init.te",
        "CompareFile":  "vendor\\init.te",
        "BaseHash":  "31411ECAFF918E28B33D5FCAF4D01D502F20E72B0BAFAA9C892C9165553910CB",
        "CompareHash":  "F13AA6701B1ADA0068E677995ABA2F9CD7B45A8B590D1940B09C57E6D6A5BC81",
        "ContentMatch":  false,
        "BaseSize":  1495,
        "CompareSize":  1367
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\kernel.te",
        "CompareFile":  "private\\kernel.te",
        "BaseHash":  "30EEE744B034D8D1FEEE6D89E0C7B833743BA546CD1E575723D50E1B39A3BE2D",
        "CompareHash":  "8892BF4F61DEC79010E9006C46BF61EA15AB988B67323924FAF598072780C737",
        "ContentMatch":  false,
        "BaseSize":  390,
        "CompareSize":  261
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\kernel.te",
        "CompareFile":  "vendor\\kernel.te",
        "BaseHash":  "30EEE744B034D8D1FEEE6D89E0C7B833743BA546CD1E575723D50E1B39A3BE2D",
        "CompareHash":  "868768EDC93CE0F2F9E1DF6B15D09829A75BC9905746568D6EA28A863A8C2B8E",
        "ContentMatch":  false,
        "BaseSize":  390,
        "CompareSize":  97
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\mediaserver.te",
        "CompareFile":  "private\\mediaserver.te",
        "BaseHash":  "A8EB744590B9C130291DB62F7D692E875EA149553FDEBBEB0D0F692F6557D7AB",
        "CompareHash":  "4C8D36F7A07C41713ACBE85C026CBEF0F36DC2BEBABFEEA8C1D1586B9DE67AD9",
        "ContentMatch":  false,
        "BaseSize":  3277,
        "CompareSize":  63
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\mtk_hal_c2.te",
        "CompareFile":  "vendor\\mtk_hal_c2.te",
        "BaseHash":  "8D3F0C267B479012827A42551981B3E85AB2A4B19506DFDF52D6386F0542EDAF",
        "CompareHash":  "77A70BDF887F4A45B0078B898DD02A4AA7156632733FECB9E8E377B725220BCD",
        "ContentMatch":  false,
        "BaseSize":  420,
        "CompareSize":  237
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\mtk_hal_camera.te",
        "CompareFile":  "vendor\\mtk_hal_camera.te",
        "BaseHash":  "CEBCC39CA40E6FED9B463961C128AD863A0FE88CAAF5358CAE2760C2F1AF9E0C",
        "CompareHash":  "3EE5F9AC2037B9ABF671317BE2C7B2BD02E264D97249CECDC335C26099C2301E",
        "ContentMatch":  false,
        "BaseSize":  3406,
        "CompareSize":  1139
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\priv_app.te",
        "CompareFile":  "private\\priv_app.te",
        "BaseHash":  "156D94260DB2D3F01C53D49685B7F3112E75066F1B61826E961EE4F19CD20D83",
        "CompareHash":  "36A025DB4FBB7BB2905F76F68B543BAF23FA954CEBE31B6AD282529D3FA6CB3D",
        "ContentMatch":  false,
        "BaseSize":  255,
        "CompareSize":  344
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "2312D49C89B81FB5F987E780E14852B4A6A6B352B4AEBADF2CBAEC333FD13B4B",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  13515,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "2312D49C89B81FB5F987E780E14852B4A6A6B352B4AEBADF2CBAEC333FD13B4B",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  13515,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "671676EC1FB58D1ADF20F3D92F867130C5D29FF7539F8BFB3C24F4A1472B2E66",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  16569,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "671676EC1FB58D1ADF20F3D92F867130C5D29FF7539F8BFB3C24F4A1472B2E66",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  16569,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\radio.te",
        "CompareFile":  "private\\radio.te",
        "BaseHash":  "35D78C65BA00A5609DF091CDD35B5B3F52B4E52A00D8B84570122EF773F178CC",
        "CompareHash":  "9404F45EA63DE877A334D4DA7D29354BF0CE30F850C0F8940E1AA6AFDCD618D6",
        "ContentMatch":  false,
        "BaseSize":  2969,
        "CompareSize":  241
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\service_contexts",
        "CompareFile":  "private\\service_contexts",
        "BaseHash":  "E53EE64E9791354BA8A9E4F135A02BC23EEF2147DE4A05B18949071CF8937C32",
        "CompareHash":  "223D6C812690FCD897D32919A26F036CBCE3A06FD6986FF4C99945312D4F3A39",
        "ContentMatch":  false,
        "BaseSize":  510,
        "CompareSize":  270
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\surfaceflinger.te",
        "CompareFile":  "vendor\\surfaceflinger.te",
        "BaseHash":  "7A7C3B33A2E902AFF4004C74D82CA00FA87C6FDF6E5263E26424EAFF8A55CB1E",
        "CompareHash":  "AF01EED496D7E94C70420E4806B02D92B3B58224DA9650FFF104656524E72A7C",
        "ContentMatch":  false,
        "BaseSize":  3514,
        "CompareSize":  177
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "C074C4FBB1973EBAFD81DD36A6139E3689D4078A4EAFF7319A00BCE151CE5194",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  5687,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "C074C4FBB1973EBAFD81DD36A6139E3689D4078A4EAFF7319A00BCE151CE5194",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  5687,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "715AD00FDC4F2C1782047C93C8B1D58689301CA1967DE0EADCA7C2D2A4A515A0",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  4221,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\untrusted_app.te",
        "CompareFile":  "private\\untrusted_app.te",
        "BaseHash":  "2285E3B0DDD791507FB244778589BAE44DE8F4E33F3B72D7AE82EC12F44BB11F",
        "CompareHash":  "6603A82BA3FAC19F9F2CAE40031BB8BF3B99EBF372984BA07E6ED296167F905A",
        "ContentMatch":  false,
        "BaseSize":  1735,
        "CompareSize":  183
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\vendor_init.te",
        "CompareFile":  "vendor\\vendor_init.te",
        "BaseHash":  "44898AACB26BFC4FAF962970ECAFEB609AF769A7CB46B17119B794B7C5753C39",
        "CompareHash":  "BE1F927E63441C8B0BA5F2A26AF01D3C691EC241085152C0F924D4473143A4D5",
        "ContentMatch":  false,
        "BaseSize":  3713,
        "CompareSize":  792
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\vpud_native.te",
        "CompareFile":  "vendor\\vpud_native.te",
        "BaseHash":  "3218B7FB665D7DE548D13E882127B06725B80459B4FE152B4FA4427742B8C981",
        "CompareHash":  "807A0BFAD1E5B0079D25EA6D7CCFB4652A3DDE6797029C883303DAB663BB4667",
        "ContentMatch":  false,
        "BaseSize":  422,
        "CompareSize":  158
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\non_plat\\vtservice.te",
        "CompareFile":  "private\\vtservice.te",
        "BaseHash":  "3AA92E1AA125F1EFDCE1814024105406DEF8D53E66F1B0F3FCD4CDFC89E44469",
        "CompareHash":  "41DDAC95AF3EE154BB0F9A7CF02CD478FD9A22E0883E309F495936BF58C381B8",
        "ContentMatch":  false,
        "BaseSize":  6152,
        "CompareSize":  52
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "D2D1B952BA41FF387BEA1FAD658CEC358BC20325C3293B1C00A2F955840ACC07",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  2387,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "D2D1B952BA41FF387BEA1FAD658CEC358BC20325C3293B1C00A2F955840ACC07",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  2387,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "CDA544D45CA179902752B337D51AFA4CC677B70B555ECB9D345B3D3B5EFE1527",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  1633,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "CDA544D45CA179902752B337D51AFA4CC677B70B555ECB9D345B3D3B5EFE1527",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  1633,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\genfs_contexts",
        "CompareFile":  "private\\genfs_contexts",
        "BaseHash":  "94FB495ED56462A11CFF11A8D87442DF208C98E828A69DA134C3D6CF82049528",
        "CompareHash":  "E1D01955A0F8688D38E1C2AD5CF53AD85CC73E4F8215FF50B6B5A20A391D6C7B",
        "ContentMatch":  false,
        "BaseSize":  15148,
        "CompareSize":  96
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\genfs_contexts",
        "CompareFile":  "vendor\\genfs_contexts",
        "BaseHash":  "94FB495ED56462A11CFF11A8D87442DF208C98E828A69DA134C3D6CF82049528",
        "CompareHash":  "FFFBB978D1843E2BD35A7D49C87CBDEB42FFCC38702EA3050F441F92311BA6BC",
        "ContentMatch":  false,
        "BaseSize":  15148,
        "CompareSize":  10601
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\init.te",
        "CompareFile":  "private\\init.te",
        "BaseHash":  "0FDA26689DE4CD45A277A320F46778F53BE8C4FFE69261D01699CC933ACB586B",
        "CompareHash":  "6778484267A1C801297B98C7FF02ABAF37BB4B7347856D0A683AED07AB386A98",
        "ContentMatch":  false,
        "BaseSize":  324,
        "CompareSize":  91
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\init.te",
        "CompareFile":  "vendor\\init.te",
        "BaseHash":  "0FDA26689DE4CD45A277A320F46778F53BE8C4FFE69261D01699CC933ACB586B",
        "CompareHash":  "F13AA6701B1ADA0068E677995ABA2F9CD7B45A8B590D1940B09C57E6D6A5BC81",
        "ContentMatch":  false,
        "BaseSize":  324,
        "CompareSize":  1367
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\mediaserver.te",
        "CompareFile":  "private\\mediaserver.te",
        "BaseHash":  "0F037ACC6E10C8C6CA9AB2BDFA70315A5DDFC5FB4E79B0CCE4310591516FABE7",
        "CompareHash":  "4C8D36F7A07C41713ACBE85C026CBEF0F36DC2BEBABFEEA8C1D1586B9DE67AD9",
        "ContentMatch":  false,
        "BaseSize":  765,
        "CompareSize":  63
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\priv_app.te",
        "CompareFile":  "private\\priv_app.te",
        "BaseHash":  "28462BAFA0473C7B85F05644C0D3092667B30713682C338E7653A279A5EABF96",
        "CompareHash":  "36A025DB4FBB7BB2905F76F68B543BAF23FA954CEBE31B6AD282529D3FA6CB3D",
        "ContentMatch":  false,
        "BaseSize":  1135,
        "CompareSize":  344
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "EA2726B3AC91AB0623D435B80B56D3879064F8C553C7DB88F462406139F03FB2",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  8236,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "EA2726B3AC91AB0623D435B80B56D3879064F8C553C7DB88F462406139F03FB2",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  8236,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "35DD6016DC671E5F1D3C810B85C538712F5B9F5AE516A9FB463EE4348866D31F",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  8136,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "35DD6016DC671E5F1D3C810B85C538712F5B9F5AE516A9FB463EE4348866D31F",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  8136,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\radio.te",
        "CompareFile":  "private\\radio.te",
        "BaseHash":  "EC02C6E8E7E2A68F5B0056E6C483D8BC7FD190126F75F91E2ACD57026306ADC2",
        "CompareHash":  "9404F45EA63DE877A334D4DA7D29354BF0CE30F850C0F8940E1AA6AFDCD618D6",
        "ContentMatch":  false,
        "BaseSize":  5370,
        "CompareSize":  241
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\service_contexts",
        "CompareFile":  "private\\service_contexts",
        "BaseHash":  "4871461C60DD1921D386537F7C75721456F2BBD65C42AB5CB15F261FE38143EE",
        "CompareHash":  "223D6C812690FCD897D32919A26F036CBCE3A06FD6986FF4C99945312D4F3A39",
        "ContentMatch":  false,
        "BaseSize":  10194,
        "CompareSize":  270
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\surfaceflinger.te",
        "CompareFile":  "vendor\\surfaceflinger.te",
        "BaseHash":  "0CA9252984C2E7F0FD62C405471418E36DAFC732ACE6E0A43DF6DBB313A14122",
        "CompareHash":  "AF01EED496D7E94C70420E4806B02D92B3B58224DA9650FFF104656524E72A7C",
        "ContentMatch":  false,
        "BaseSize":  1530,
        "CompareSize":  177
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\system_app.te",
        "CompareFile":  "private\\system_app.te",
        "BaseHash":  "ECB71AA797256943A2EDA6C262B8AD6F25E20F844543FD5664B803E9080B3D14",
        "CompareHash":  "893242D348FC30788945791E1FB64E472BC04C13F577B579FA9FFE8D983E64BB",
        "ContentMatch":  false,
        "BaseSize":  3881,
        "CompareSize":  165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\system_app.te",
        "CompareFile":  "vendor\\system_app.te",
        "BaseHash":  "ECB71AA797256943A2EDA6C262B8AD6F25E20F844543FD5664B803E9080B3D14",
        "CompareHash":  "7DF06BD56F079E182FB3514694AEDC31A56E133C560A5400188BAD293E1BB306",
        "ContentMatch":  false,
        "BaseSize":  3881,
        "CompareSize":  681
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\system_server.te",
        "CompareFile":  "vendor\\system_server.te",
        "BaseHash":  "219932123EB4D6CB75712709943E4EFE98C4465E4D53F04CD513E5FC5982ADA1",
        "CompareHash":  "8842F80C341FF7CF90698D0F83C685158F2CAA468AAAFE9C7BE6260C92542F6E",
        "ContentMatch":  false,
        "BaseSize":  5113,
        "CompareSize":  481
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\untrusted_app.te",
        "CompareFile":  "private\\untrusted_app.te",
        "BaseHash":  "4DAD19FBF6EFE262096C349F8FA386356C7A51D96153FFA05D63875D8A279410",
        "CompareHash":  "6603A82BA3FAC19F9F2CAE40031BB8BF3B99EBF372984BA07E6ED296167F905A",
        "ContentMatch":  false,
        "BaseSize":  432,
        "CompareSize":  183
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\vendor_init.te",
        "CompareFile":  "vendor\\vendor_init.te",
        "BaseHash":  "6C3A6F9AB8BAFEE8C63A4CFF48262C90E5A884302FB91D976FAC057EDF325727",
        "CompareHash":  "BE1F927E63441C8B0BA5F2A26AF01D3C691EC241085152C0F924D4473143A4D5",
        "ContentMatch":  false,
        "BaseSize":  244,
        "CompareSize":  792
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_private\\vtservice.te",
        "CompareFile":  "private\\vtservice.te",
        "BaseHash":  "7583CEC9BDE5B813FD8EE7FBF3BECDABCA8D554A55C38361A2EA2B23D9F0B260",
        "CompareHash":  "41DDAC95AF3EE154BB0F9A7CF02CD478FD9A22E0883E309F495936BF58C381B8",
        "ContentMatch":  false,
        "BaseSize":  763,
        "CompareSize":  52
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_public\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "09A4DB458870AB3BABD3A82AA6538CBCD0055CD8032CDD9A9562CBCB235AAF07",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  342,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_public\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "09A4DB458870AB3BABD3A82AA6538CBCD0055CD8032CDD9A9562CBCB235AAF07",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  342,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_public\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "899C33F17D5ACC2D720B3F5F1A20162B0DB3085730B2EA793346C46EA8CFF00C",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  859,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "bsp\\plat_public\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "899C33F17D5ACC2D720B3F5F1A20162B0DB3085730B2EA793346C46EA8CFF00C",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  859,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "legacy\\non_plat\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "230F3D9D52A0992A29C6FC0F061571897308CE92D6FC350D5A50D6E11B339532",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  424,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "legacy\\non_plat\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "230F3D9D52A0992A29C6FC0F061571897308CE92D6FC350D5A50D6E11B339532",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  424,
        "CompareSize":  1165
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\file.te",
        "CompareFile":  "public\\file.te",
        "BaseHash":  "851566860A5181D5B86191B37437FF11120A72EF103F14CF228C411D518814CD",
        "CompareHash":  "C4A3FA36901ADA92ADBD121BB7488DF639EF48659CFA521EE98B0E95C211FB44",
        "ContentMatch":  false,
        "BaseSize":  230,
        "CompareSize":  68
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\file.te",
        "CompareFile":  "vendor\\file.te",
        "BaseHash":  "851566860A5181D5B86191B37437FF11120A72EF103F14CF228C411D518814CD",
        "CompareHash":  "BF809657358C1136818755BB7A301D8E4C04CEF4A6032814A760939CFFF304F2",
        "ContentMatch":  false,
        "BaseSize":  230,
        "CompareSize":  1979
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\file_contexts",
        "CompareFile":  "private\\file_contexts",
        "BaseHash":  "06DE02F295283665CEC9949EB09CC13ED08EB43F62664E35BE8E3CAA979D5273",
        "CompareHash":  "EA0680EFFA46CB783011E83239CF4927028A436C8D973DACE08DF36CAACFCC95",
        "ContentMatch":  false,
        "BaseSize":  1787,
        "CompareSize":  246
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\file_contexts",
        "CompareFile":  "vendor\\file_contexts",
        "BaseHash":  "06DE02F295283665CEC9949EB09CC13ED08EB43F62664E35BE8E3CAA979D5273",
        "CompareHash":  "43406AC4D419459A8B404FA5EE345F286E69F766AE9ACFD8C58AA94A45FD1129",
        "ContentMatch":  false,
        "BaseSize":  1787,
        "CompareSize":  7603
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\property.te",
        "CompareFile":  "public\\property.te",
        "BaseHash":  "2558C3460945F16373836EC97AF118B2E01F965A57DBC9D4B963E495B2103B4F",
        "CompareHash":  "26E4CE56111904CE8FC98C93FAFE0AC3CD300088301242AF4ADA67E3F1EC74F8",
        "ContentMatch":  false,
        "BaseSize":  713,
        "CompareSize":  273
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\property.te",
        "CompareFile":  "vendor\\property.te",
        "BaseHash":  "2558C3460945F16373836EC97AF118B2E01F965A57DBC9D4B963E495B2103B4F",
        "CompareHash":  "918A0260ABB22AE396BCD2F21F10ADB50C8DE40C13FF6745F45D2F847D99E595",
        "ContentMatch":  false,
        "BaseSize":  713,
        "CompareSize":  240
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\property_contexts",
        "CompareFile":  "private\\property_contexts",
        "BaseHash":  "1DC6CBB07E6561F76277684880D1EB313E27DAA918917113EC6AD0F973608802",
        "CompareHash":  "68259467ADFE811F47E9A92E2208355F98B1A385FC241133CE44AF9E2018642A",
        "ContentMatch":  false,
        "BaseSize":  809,
        "CompareSize":  3263
    },
    {
        "Type":  "ExactName",
        "BaseFile":  "modem\\property_contexts",
        "CompareFile":  "vendor\\property_contexts",
        "BaseHash":  "1DC6CBB07E6561F76277684880D1EB313E27DAA918917113EC6AD0F973608802",
        "CompareHash":  "D6E9A44CEDF00D10762B5686BED43F9BC0CCF1EDA6806027460503FA4AD4CF6A",
        "ContentMatch":  false,
        "BaseSize":  809,
        "CompareSize":  1165
    }
]
