# ==============================================
# Common SEPolicy Rule
# ==============================================

allow netutils_wrapper ipsec_mon:fd use;
allow netutils_wrapper ipsec_mon:netlink_route_socket { read write };
allow netutils_wrapper ipsec_mon:netlink_xfrm_socket { read write };
allow netutils_wrapper devpts:chr_file { getattr ioctl read write };

allow netutils_wrapper netdagent:fd use;
allow netutils_wrapper netdagent:unix_stream_socket { read write };

allow netutils_wrapper rild:fd use;
allow netutils_wrapper rild:unix_stream_socket { read write };
allow netutils_wrapper rild:fifo_file rw_file_perms;

allow netutils_wrapper wo_epdg_client:unix_stream_socket { read write };
allow netutils_wrapper wo_epdg_client:fd use;

allow netutils_wrapper {
    gsm0710muxd_device
    ccci_vts_device
    ccci_wifi_proxy_device
    ccci_device
}:chr_file rw_file_perms;
