07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:28): avc:  denied  { relabelfrom } for  name="skip_mount" dev="dm-38" ino=53371 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:29): avc:  denied  { relabelfrom } for  name="common" dev="dm-38" ino=52790 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:30): avc:  denied  { relabelfrom } for  name="repo.json" dev="dm-38" ino=52792 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:31): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=52801 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:32): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=52803 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:33): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=52804 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:34): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=52807 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:35): avc:  denied  { relabelfrom } for  name="utils.sh" dev="dm-38" ino=52810 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:36): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=52813 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:37): avc:  denied  { relabelfrom } for  name="styles" dev="dm-38" ino=52814 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:38): avc:  denied  { relabelfrom } for  name="docs.css" dev="dm-38" ino=52819 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:39): avc:  denied  { relabelfrom } for  name="styles.css" dev="dm-38" ino=52825 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:40): avc:  denied  { relabelfrom } for  name="markdown.css" dev="dm-38" ino=52828 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:41): avc:  denied  { relabelfrom } for  name="color.css" dev="dm-38" ino=52829 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:42): avc:  denied  { relabelfrom } for  name="file_selector.css" dev="dm-38" ino=52830 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:43): avc:  denied  { relabelfrom } for  name="icon.png" dev="dm-38" ino=52838 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:44): avc:  denied  { relabelfrom } for  name="more.html" dev="dm-38" ino=52841 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:45): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=52842 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:46): avc:  denied  { relabelfrom } for  name="scripts" dev="dm-38" ino=52843 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.612     1     1 W init    : type=1400 audit(0.0:47): avc:  denied  { relabelfrom } for  name="more.js" dev="dm-38" ino=52844 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:48): avc:  denied  { relabelfrom } for  name="index.js" dev="dm-38" ino=52847 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:49): avc:  denied  { relabelfrom } for  name="docs.js" dev="dm-38" ino=52848 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:50): avc:  denied  { relabelfrom } for  name="util.js" dev="dm-38" ino=52849 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:51): avc:  denied  { relabelfrom } for  name="language.js" dev="dm-38" ino=52851 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:52): avc:  denied  { relabelfrom } for  name="hosts.js" dev="dm-38" ino=52853 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:53): avc:  denied  { relabelfrom } for  name="file_selector.js" dev="dm-38" ino=52855 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:54): avc:  denied  { relabelfrom } for  name="hosts.html" dev="dm-38" ino=52856 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:55): avc:  denied  { relabelfrom } for  name="config.json" dev="dm-38" ino=52857 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:56): avc:  denied  { relabelfrom } for  name="locales" dev="dm-38" ino=52858 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:57): avc:  denied  { relabelfrom } for  name="template.xml" dev="dm-38" ino=52859 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:58): avc:  denied  { relabelfrom } for  name="languages.json" dev="dm-38" ino=52861 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:59): avc:  denied  { relabelfrom } for  name="strings" dev="dm-38" ino=52863 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:60): avc:  denied  { relabelfrom } for  name="en.xml" dev="dm-38" ino=52865 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:61): avc:  denied  { relabelfrom } for  name="ar.xml" dev="dm-38" ino=52867 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:62): avc:  denied  { relabelfrom } for  name="it.xml" dev="dm-38" ino=52869 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:63): avc:  denied  { relabelfrom } for  name="ja.xml" dev="dm-38" ino=52872 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:64): avc:  denied  { relabelfrom } for  name="es.xml" dev="dm-38" ino=52874 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:65): avc:  denied  { relabelfrom } for  name="id.xml" dev="dm-38" ino=52875 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:66): avc:  denied  { relabelfrom } for  name="uk.xml" dev="dm-38" ino=52876 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:67): avc:  denied  { relabelfrom } for  name="cs.xml" dev="dm-38" ino=52877 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:68): avc:  denied  { relabelfrom } for  name="el.xml" dev="dm-38" ino=52878 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:69): avc:  denied  { relabelfrom } for  name="tr.xml" dev="dm-38" ino=52879 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:70): avc:  denied  { relabelfrom } for  name="zh-TW.xml" dev="dm-38" ino=52882 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:71): avc:  denied  { relabelfrom } for  name="sv.xml" dev="dm-38" ino=52884 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:72): avc:  denied  { relabelfrom } for  name="de.xml" dev="dm-38" ino=52885 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:73): avc:  denied  { relabelfrom } for  name="pl.xml" dev="dm-38" ino=52887 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:74): avc:  denied  { relabelfrom } for  name="ru.xml" dev="dm-38" ino=52892 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.616     1     1 W init    : type=1400 audit(0.0:75): avc:  denied  { relabelfrom } for  name="zh-CN.xml" dev="dm-38" ino=52893 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:76): avc:  denied  { relabelfrom } for  name="link" dev="dm-38" ino=53746 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:77): avc:  denied  { relabelfrom } for  name="hosts.txt" dev="dm-38" ino=57788 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=lnk_file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:78): avc:  denied  { relabelfrom } for  name="MODDIR" dev="dm-38" ino=53749 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=lnk_file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:79): avc:  denied  { relabelfrom } for  name="PERSISTENT_DIR" dev="dm-38" ino=53750 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=lnk_file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:80): avc:  denied  { relabelfrom } for  name="system" dev="dm-38" ino=52895 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:81): avc:  denied  { relabelfrom } for  name="etc" dev="dm-38" ino=52897 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:82): avc:  denied  { relabelfrom } for  name="hosts" dev="dm-38" ino=52899 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:83): avc:  denied  { relabelfrom } for  name="zygisk_lsposed" dev="dm-38" ino=42150 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:84): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=42152 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:85): avc:  denied  { relabelfrom } for  name="action.sh" dev="dm-38" ino=42153 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:86): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=42154 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:87): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=42155 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:88): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=42156 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:89): avc:  denied  { relabelfrom } for  name="sepolicy.rule" dev="dm-38" ino=42157 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:90): avc:  denied  { relabelfrom } for  name="framework" dev="dm-38" ino=42158 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:91): avc:  denied  { relabelfrom } for  name="lspd.dex" dev="dm-38" ino=42159 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:92): avc:  denied  { relabelfrom } for  name="daemon.apk" dev="dm-38" ino=42174 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:93): avc:  denied  { relabelfrom } for  name="daemon" dev="dm-38" ino=42161 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:94): avc:  denied  { relabelfrom } for  name="manager.apk" dev="dm-38" ino=42162 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.620     1     1 W init    : type=1400 audit(0.0:95): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=42163 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:96): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=42164 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:97): avc:  denied  { relabelfrom } for  name="src.eb40865b.js" dev="dm-38" ino=42165 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:98): avc:  denied  { relabelfrom } for  name="zygisk" dev="dm-38" ino=42166 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:99): avc:  denied  { relabelfrom } for  name="armeabi-v7a.so" dev="dm-38" ino=42167 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:100): avc:  denied  { relabelfrom } for  name="arm64-v8a.so" dev="dm-38" ino=42168 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:101): avc:  denied  { getattr } for  path="/data/adb/modules/zygisk_lsposed/bin/dex2oat32" dev="dm-38" ino=42175 scontext=u:r:init:s0 tcontext=u:object_r:dex2oat_exec:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:102): avc:  denied  { getattr } for  path="/data/adb/modules/zygisk_lsposed/bin/dex2oat64" dev="dm-38" ino=42176 scontext=u:r:init:s0 tcontext=u:object_r:dex2oat_exec:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:103): avc:  denied  { relabelfrom } for  name="yt_rvx" dev="dm-38" ino=42294 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:104): avc:  denied  { relabelfrom } for  name="config" dev="dm-38" ino=42356 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:105): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=42357 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:106): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=42358 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:107): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=42359 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:108): avc:  denied  { relabelfrom } for  name="zygisksu" dev="dm-38" ino=42547 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:109): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=42549 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:110): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=42550 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:111): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=42551 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:112): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=42552 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:113): avc:  denied  { relabelfrom } for  name="mazoku" dev="dm-38" ino=42553 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:114): avc:  denied  { relabelfrom } for  name="cleanup.sh" dev="dm-38" ino=42554 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:115): avc:  denied  { relabelfrom } for  name="sepolicy.rule" dev="dm-38" ino=42555 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.624     1     1 W init    : type=1400 audit(0.0:116): avc:  denied  { relabelfrom } for  name="module.prop.orig" dev="dm-38" ino=42556 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:117): avc:  denied  { relabelfrom } for  name="bin" dev="dm-38" ino=42557 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:118): avc:  denied  { relabelfrom } for  name="zygiskd" dev="dm-38" ino=42573 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=lnk_file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:119): avc:  denied  { relabelfrom } for  name="zygiskd32" dev="dm-38" ino=42565 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:120): avc:  denied  { relabelfrom } for  name="zygiskd64" dev="dm-38" ino=42569 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:121): avc:  denied  { relabelfrom } for  name="lib" dev="dm-38" ino=42558 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:122): avc:  denied  { relabelfrom } for  name="libzygisk.so" dev="dm-38" ino=42566 scontext=u:r:init:s0 tcontext=u:object_r:system_lib_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:123): avc:  denied  { relabelfrom } for  name="libzn_loader.so" dev="dm-38" ino=42568 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:124): avc:  denied  { relabelfrom } for  name="lib64" dev="dm-38" ino=42559 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:125): avc:  denied  { relabelfrom } for  name="libzygisk.so" dev="dm-38" ino=42570 scontext=u:r:init:s0 tcontext=u:object_r:system_lib_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:126): avc:  denied  { relabelfrom } for  name="libpayload.so" dev="dm-38" ino=42571 scontext=u:r:init:s0 tcontext=u:object_r:system_lib_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:127): avc:  denied  { relabelfrom } for  name="libzn_loader.so" dev="dm-38" ino=42572 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:128): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=42560 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:129): avc:  denied  { relabelfrom } for  name="assets" dev="dm-38" ino=42561 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:130): avc:  denied  { relabelfrom } for  name="index-D2eQPPgU.js" dev="dm-38" ino=42562 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:131): avc:  denied  { relabelfrom } for  name="index-D2o6wlsd.css" dev="dm-38" ino=42563 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:132): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=42564 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:133): avc:  denied  { relabelfrom } for  name="machikado" dev="dm-38" ino=42574 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.628     1     1 W init    : type=1400 audit(0.0:134): avc:  denied  { relabelfrom } for  name="susfs4ksu" dev="dm-38" ino=42674 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:135): avc:  denied  { relabelfrom } for  name="CHANGELOG.md" dev="dm-38" ino=42676 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:136): avc:  denied  { relabelfrom } for  name="LICENSE" dev="dm-38" ino=42677 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:137): avc:  denied  { relabelfrom } for  name="action.sh" dev="dm-38" ino=42679 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:138): avc:  denied  { relabelfrom } for  name="banner.png" dev="dm-38" ino=42680 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:139): avc:  denied  { relabelfrom } for  name="boot-completed.sh" dev="dm-38" ino=42681 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:140): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=57782 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:141): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=42684 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:142): avc:  denied  { relabelfrom } for  name="post-mount.sh" dev="dm-38" ino=42685 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:143): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=42686 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:144): avc:  denied  { relabelfrom } for  name="susfs-bin-check.sh" dev="dm-38" ino=42689 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:145): avc:  denied  { relabelfrom } for  name="susfs-bin-update.sh" dev="dm-38" ino=42690 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:146): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=42705 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:147): avc:  denied  { relabelfrom } for  name="update.json" dev="dm-38" ino=42706 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:148): avc:  denied  { relabelfrom } for  name="utils.sh" dev="dm-38" ino=42707 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:149): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=42708 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:150): avc:  denied  { relabelfrom } for  name="assets" dev="dm-38" ino=42709 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:151): avc:  denied  { relabelfrom } for  name="AmaticSC-Bold-t5U5bEu8.ttf" dev="dm-38" ino=42710 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:152): avc:  denied  { relabelfrom } for  name="AmaticSC-Regular-CCetldNi.ttf" dev="dm-38" ino=42711 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:153): avc:  denied  { relabelfrom } for  name="Impostograph-Regular-DKWEku3n.ttf" dev="dm-38" ino=42712 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:154): avc:  denied  { relabelfrom } for  name="black-CWwxUpmx.png" dev="dm-38" ino=42713 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:155): avc:  denied  { relabelfrom } for  name="blue-CDd1JgFA.png" dev="dm-38" ino=42714 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:156): avc:  denied  { relabelfrom } for  name="brown-DkMF-zd4.png" dev="dm-38" ino=42715 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:157): avc:  denied  { relabelfrom } for  name="creditsjs-BfjoyHvA.js" dev="dm-38" ino=42716 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:158): avc:  denied  { relabelfrom } for  name="cyan-CxxU9VUc.png" dev="dm-38" ino=42717 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:159): avc:  denied  { relabelfrom } for  name="cyan1-BWTE1JdY.png" dev="dm-38" ino=42718 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.632     1     1 W init    : type=1400 audit(0.0:160): avc:  denied  { relabelfrom } for  name="fade-4ApaDT9x.js" dev="dm-38" ino=42719 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:161): avc:  denied  { relabelfrom } for  name="green-BwBzMX7_.png" dev="dm-38" ino=42720 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:162): avc:  denied  { relabelfrom } for  name="i18n-BqJdyLAD.js" dev="dm-38" ino=42721 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:163): avc:  denied  { relabelfrom } for  name="index-C9VpSQPu.css" dev="dm-38" ino=42722 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:164): avc:  denied  { relabelfrom } for  name="index-aRNjZnqX.js" dev="dm-38" ino=42723 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:165): avc:  denied  { relabelfrom } for  name="lime-DPDNUj7v.png" dev="dm-38" ino=42724 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:166): avc:  denied  { relabelfrom } for  name="orange-Dz5Wr5v6.png" dev="dm-38" ino=42725 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:167): avc:  denied  { relabelfrom } for  name="orange1-CeH2ZIyG.png" dev="dm-38" ino=42726 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:168): avc:  denied  { relabelfrom } for  name="pink-BsJVk9ad.png" dev="dm-38" ino=42727 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:169): avc:  denied  { relabelfrom } for  name="purple-b3Qaozit.png" dev="dm-38" ino=42728 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:170): avc:  denied  { relabelfrom } for  name="red-DsgSinuJ.png" dev="dm-38" ino=42729 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:171): avc:  denied  { relabelfrom } for  name="susfs_error-BRcgEdpI.png" dev="dm-38" ino=42730 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:172): avc:  denied  { relabelfrom } for  name="susfs_update-BC5m6nLp.png" dev="dm-38" ino=42731 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:173): avc:  denied  { relabelfrom } for  name="white-BOZMyur9.png" dev="dm-38" ino=42732 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:174): avc:  denied  { relabelfrom } for  name="yellow-CMDT5nHw.png" dev="dm-38" ino=42733 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:175): avc:  denied  { relabelfrom } for  name="yellow1-Bmr_vY2u.png" dev="dm-38" ino=42734 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:176): avc:  denied  { relabelfrom } for  name="config.json" dev="dm-38" ino=42735 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:177): avc:  denied  { relabelfrom } for  name="contributors.json" dev="dm-38" ino=42736 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:178): avc:  denied  { relabelfrom } for  name="credits.html" dev="dm-38" ino=42737 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:179): avc:  denied  { relabelfrom } for  name="custom.html" dev="dm-38" ino=42738 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:180): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=42739 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:181): avc:  denied  { relabelfrom } for  name="languages" dev="dm-38" ino=42740 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:182): avc:  denied  { relabelfrom } for  name="ar.xml" dev="dm-38" ino=42741 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:183): avc:  denied  { relabelfrom } for  name="de.xml" dev="dm-38" ino=42742 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:184): avc:  denied  { relabelfrom } for  name="en.xml" dev="dm-38" ino=42743 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:185): avc:  denied  { relabelfrom } for  name="es.xml" dev="dm-38" ino=42745 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:186): avc:  denied  { relabelfrom } for  name="fa.xml" dev="dm-38" ino=42746 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:187): avc:  denied  { relabelfrom } for  name="fr.xml" dev="dm-38" ino=42747 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:188): avc:  denied  { relabelfrom } for  name="id.xml" dev="dm-38" ino=42748 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:189): avc:  denied  { relabelfrom } for  name="it.xml" dev="dm-38" ino=42749 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:190): avc:  denied  { relabelfrom } for  name="ja.xml" dev="dm-38" ino=42750 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:191): avc:  denied  { relabelfrom } for  name="languages.json" dev="dm-38" ino=42751 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:192): avc:  denied  { relabelfrom } for  name="pl.xml" dev="dm-38" ino=42752 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:193): avc:  denied  { relabelfrom } for  name="ptbr.xml" dev="dm-38" ino=42753 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:194): avc:  denied  { relabelfrom } for  name="ru.xml" dev="dm-38" ino=42754 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.636     1     1 W init    : type=1400 audit(0.0:195): avc:  denied  { relabelfrom } for  name="tr.xml" dev="dm-38" ino=42755 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:196): avc:  denied  { relabelfrom } for  name="vi.xml" dev="dm-38" ino=42756 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:197): avc:  denied  { relabelfrom } for  name="zh-rCN.xml" dev="dm-38" ino=42757 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:198): avc:  denied  { relabelfrom } for  name="zh-rTW.xml" dev="dm-38" ino=42758 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:199): avc:  denied  { relabelfrom } for  name="status.html" dev="dm-38" ino=42759 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:200): avc:  denied  { relabelfrom } for  name="translators.json" dev="dm-38" ino=42760 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:201): avc:  denied  { relabelfrom } for  name="tricky_store" dev="dm-38" ino=42829 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:202): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=42831 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:203): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=42832 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:204): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=42833 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:205): avc:  denied  { relabelfrom } for  name="service.apk" dev="dm-38" ino=42834 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:206): avc:  denied  { relabelfrom } for  name="sepolicy.rule" dev="dm-38" ino=42835 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:207): avc:  denied  { relabelfrom } for  name="daemon" dev="dm-38" ino=42836 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:208): avc:  denied  { relabelfrom } for  name="mazoku" dev="dm-38" ino=42837 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:209): avc:  denied  { relabelfrom } for  name="libtricky_store.so" dev="dm-38" ino=42838 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:210): avc:  denied  { relabelfrom } for  name="machikado" dev="dm-38" ino=42839 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:211): avc:  denied  { relabelfrom } for  name="playintegrityfix" dev="dm-38" ino=52549 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:212): avc:  denied  { relabelfrom } for  name="action.sh.old" dev="dm-38" ino=52552 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:213): avc:  denied  { relabelfrom } for  name="autopif.sh" dev="dm-38" ino=52551 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:214): avc:  denied  { relabelfrom } for  name="autopif_ota.sh" dev="dm-38" ino=52553 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:215): avc:  denied  { relabelfrom } for  name="classes.dex" dev="dm-38" ino=52557 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:216): avc:  denied  { relabelfrom } for  name="common_func.sh" dev="dm-38" ino=52558 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:217): avc:  denied  { relabelfrom } for  name="inject" dev="dm-38" ino=52560 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:218): avc:  denied  { relabelfrom } for  name="arm64-v8a.so" dev="dm-38" ino=52561 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:219): avc:  denied  { relabelfrom } for  name="armeabi-v7a.so" dev="dm-38" ino=52563 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:220): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=52570 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.640     1     1 W init    : type=1400 audit(0.0:221): avc:  denied  { relabelfrom } for  name="pif.prop" dev="dm-38" ino=52603 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:222): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=52574 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:223): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=52576 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:224): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=52578 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:225): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=52579 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:226): avc:  denied  { relabelfrom } for  name="assets" dev="dm-38" ino=52581 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:227): avc:  denied  { relabelfrom } for  name="kernelsu.js" dev="dm-38" ino=52582 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:228): avc:  denied  { relabelfrom } for  name="config.json" dev="dm-38" ino=52583 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:229): avc:  denied  { relabelfrom } for  name="icon.jpg" dev="dm-38" ino=52584 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:230): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=52585 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:231): avc:  denied  { relabelfrom } for  name="scripts.js" dev="dm-38" ino=52586 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:232): avc:  denied  { relabelfrom } for  name="styles.css" dev="dm-38" ino=52588 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:233): avc:  denied  { relabelfrom } for  name="zygisk" dev="dm-38" ino=52590 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:234): avc:  denied  { relabelfrom } for  name="arm64-v8a.so" dev="dm-38" ino=52591 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:235): avc:  denied  { relabelfrom } for  name="armeabi-v7a.so" dev="dm-38" ino=52592 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:236): avc:  denied  { relabelfrom } for  name="Yurikey" dev="dm-38" ino=53157 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:237): avc:  denied  { relabelfrom } for  name="Yuri" dev="dm-38" ino=53161 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:238): avc:  denied  { relabelfrom } for  name="boot_hash.sh" dev="dm-38" ino=53162 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:239): avc:  denied  { relabelfrom } for  name="kill_all.sh" dev="dm-38" ino=53163 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.644     1     1 W init    : type=1400 audit(0.0:240): avc:  denied  { relabelfrom } for  name="kill_google_process.sh" dev="dm-38" ino=53169 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:241): avc:  denied  { relabelfrom } for  name="security_patch.sh" dev="dm-38" ino=53170 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:242): avc:  denied  { relabelfrom } for  name="select_app_neccesary.sh" dev="dm-38" ino=53173 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:243): avc:  denied  { relabelfrom } for  name="target_txt.sh" dev="dm-38" ino=53175 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:244): avc:  denied  { relabelfrom } for  name="yuri_keybox.sh" dev="dm-38" ino=53176 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:245): avc:  denied  { relabelfrom } for  name="action.sh" dev="dm-38" ino=53179 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:246): avc:  denied  { relabelfrom } for  name="banner.png" dev="dm-38" ino=53181 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:247): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=53182 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:248): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=53184 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:249): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=53186 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:250): avc:  denied  { relabelfrom } for  name="common" dev="dm-38" ino=53188 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:251): avc:  denied  { relabelfrom } for  name="assets" dev="dm-38" ino=53190 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:252): avc:  denied  { relabelfrom } for  name="icon" dev="dm-38" ino=53191 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:253): avc:  denied  { relabelfrom } for  name="github.gif" dev="dm-38" ino=53192 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:254): avc:  denied  { relabelfrom } for  name="play.svg" dev="dm-38" ino=53195 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:255): avc:  denied  { relabelfrom } for  name="refresh.svg" dev="dm-38" ino=53199 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:256): avc:  denied  { relabelfrom } for  name="telegram.gif" dev="dm-38" ino=53200 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.648     1     1 W init    : type=1400 audit(0.0:257): avc:  denied  { relabelfrom } for  name="navbar" dev="dm-38" ino=53202 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:258): avc:  denied  { relabelfrom } for  name="android.png" dev="dm-38" ino=53203 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:259): avc:  denied  { relabelfrom } for  name="community.png" dev="dm-38" ino=53204 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:260): avc:  denied  { relabelfrom } for  name="home.svg" dev="dm-38" ino=53206 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:261): avc:  denied  { relabelfrom } for  name="menu.svg" dev="dm-38" ino=53208 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:262): avc:  denied  { relabelfrom } for  name="settings.svg" dev="dm-38" ino=53210 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:263): avc:  denied  { relabelfrom } for  name="wallpaper" dev="dm-38" ino=53214 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:264): avc:  denied  { relabelfrom } for  name="sparkle.png" dev="dm-38" ino=53219 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:265): avc:  denied  { relabelfrom } for  name="boot_hash.sh" dev="dm-38" ino=53224 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:266): avc:  denied  { relabelfrom } for  name="device-info.sh" dev="dm-38" ino=53226 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:267): avc:  denied  { relabelfrom } for  name="hma.sh" dev="dm-38" ino=53227 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:268): avc:  denied  { relabelfrom } for  name="lsposed2.sh" dev="dm-38" ino=53228 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:269): avc:  denied  { relabelfrom } for  name="pif2.sh" dev="dm-38" ino=53229 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:270): avc:  denied  { relabelfrom } for  name="theme-manager.sh" dev="dm-38" ino=53230 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:271): avc:  denied  { relabelfrom } for  name="twrp.sh" dev="dm-38" ino=53231 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:272): avc:  denied  { relabelfrom } for  name="config.json" dev="dm-38" ino=53232 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:273): avc:  denied  { relabelfrom } for  name="css" dev="dm-38" ino=53233 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:274): avc:  denied  { relabelfrom } for  name="style.css" dev="dm-38" ino=53234 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:275): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=53235 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:276): avc:  denied  { relabelfrom } for  name="js" dev="dm-38" ino=53236 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:277): avc:  denied  { relabelfrom } for  name="dev.js" dev="dm-38" ino=53237 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:278): avc:  denied  { relabelfrom } for  name="device.js" dev="dm-38" ino=53238 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:279): avc:  denied  { relabelfrom } for  name="language.js" dev="dm-38" ino=53239 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:280): avc:  denied  { relabelfrom } for  name="main.js" dev="dm-38" ino=53240 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:281): avc:  denied  { relabelfrom } for  name="redirect.js" dev="dm-38" ino=53241 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:282): avc:  denied  { relabelfrom } for  name="theme.js" dev="dm-38" ino=53242 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.652     1     1 W init    : type=1400 audit(0.0:283): avc:  denied  { relabelfrom } for  name="version.js" dev="dm-38" ino=53243 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:284): avc:  denied  { relabelfrom } for  name="json" dev="dm-38" ino=53244 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:285): avc:  denied  { relabelfrom } for  name="dev.json" dev="dm-38" ino=53245 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:286): avc:  denied  { relabelfrom } for  name="device-info.json" dev="dm-38" ino=53248 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:287): avc:  denied  { relabelfrom } for  name="theme-config.json" dev="dm-38" ino=53250 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:288): avc:  denied  { relabelfrom } for  name="lang" dev="dm-38" ino=53251 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:289): avc:  denied  { relabelfrom } for  name="ar.json" dev="dm-38" ino=53254 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:290): avc:  denied  { relabelfrom } for  name="bn.json" dev="dm-38" ino=53255 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:291): avc:  denied  { relabelfrom } for  name="en.json" dev="dm-38" ino=53256 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:292): avc:  denied  { relabelfrom } for  name="es.json" dev="dm-38" ino=53257 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:293): avc:  denied  { relabelfrom } for  name="hi.json" dev="dm-38" ino=53258 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:294): avc:  denied  { relabelfrom } for  name="hu.json" dev="dm-38" ino=53259 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:295): avc:  denied  { relabelfrom } for  name="id.json" dev="dm-38" ino=53261 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:296): avc:  denied  { relabelfrom } for  name="jp.json" dev="dm-38" ino=53262 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:297): avc:  denied  { relabelfrom } for  name="ms.json" dev="dm-38" ino=53263 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:298): avc:  denied  { relabelfrom } for  name="ph.json" dev="dm-38" ino=53264 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:299): avc:  denied  { relabelfrom } for  name="pl.json" dev="dm-38" ino=53265 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:300): avc:  denied  { relabelfrom } for  name="pt-br.json" dev="dm-38" ino=53266 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:301): avc:  denied  { relabelfrom } for  name="ro-md.json" dev="dm-38" ino=53267 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:302): avc:  denied  { relabelfrom } for  name="ru.json" dev="dm-38" ino=53268 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:303): avc:  denied  { relabelfrom } for  name="th.json" dev="dm-38" ino=53269 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:304): avc:  denied  { relabelfrom } for  name="tr.json" dev="dm-38" ino=53270 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:305): avc:  denied  { relabelfrom } for  name="uka.json" dev="dm-38" ino=53271 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:306): avc:  denied  { relabelfrom } for  name="vn.json" dev="dm-38" ino=53272 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:307): avc:  denied  { relabelfrom } for  name="zh-han.json" dev="dm-38" ino=53275 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:308): avc:  denied  { relabelfrom } for  name="yurikey.png" dev="dm-38" ino=53276 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.656     1     1 W init    : type=1400 audit(0.0:309): avc:  denied  { relabelfrom } for  name="integrity_box" dev="dm-38" ino=57448 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:310): avc:  denied  { relabelfrom } for  name="customize.sh" dev="dm-38" ino=57449 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:311): avc:  denied  { relabelfrom } for  name=".mona" dev="dm-38" ino=57450 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:312): avc:  denied  { relabelfrom } for  name="README.md" dev="dm-38" ino=57451 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:313): avc:  denied  { relabelfrom } for  name="action.sh" dev="dm-38" ino=57452 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:314): avc:  denied  { relabelfrom } for  name="boot-completed.sh" dev="dm-38" ino=57453 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:315): avc:  denied  { relabelfrom } for  name="cleanup.sh" dev="dm-38" ino=57454 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:316): avc:  denied  { relabelfrom } for  name="credits.md" dev="dm-38" ino=57455 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:317): avc:  denied  { relabelfrom } for  name="custom.pif.json" dev="dm-38" ino=57456 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:318): avc:  denied  { relabelfrom } for  name="hashes.txt" dev="dm-38" ino=57457 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:319): avc:  denied  { relabelfrom } for  name="keybox.xml" dev="dm-38" ino=57458 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:320): avc:  denied  { relabelfrom } for  name="keycheck" dev="dm-38" ino=57459 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:321): avc:  denied  { relabelfrom } for  name="module.prop" dev="dm-38" ino=57345 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:322): avc:  denied  { relabelfrom } for  name="pif.prop" dev="dm-38" ino=57461 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:323): avc:  denied  { relabelfrom } for  name="post-fs-data.sh" dev="dm-38" ino=57462 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:324): avc:  denied  { relabelfrom } for  name="service.sh" dev="dm-38" ino=57463 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:325): avc:  denied  { relabelfrom } for  name="system.prop" dev="dm-38" ino=57464 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:326): avc:  denied  { relabelfrom } for  name="system" dev="dm-38" ino=57465 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:327): avc:  denied  { relabelfrom } for  name="product" dev="dm-38" ino=57466 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:328): avc:  denied  { relabelfrom } for  name="app" dev="dm-38" ino=57467 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:329): avc:  denied  { relabelfrom } for  name="Toaster" dev="dm-38" ino=57468 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:330): avc:  denied  { relabelfrom } for  name="Toaster.apk" dev="dm-38" ino=57469 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:331): avc:  denied  { relabelfrom } for  name="uninstall.sh" dev="dm-38" ino=57470 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.660     1     1 W init    : type=1400 audit(0.0:332): avc:  denied  { relabelfrom } for  name="webroot" dev="dm-38" ino=57471 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:333): avc:  denied  { relabelfrom } for  name="common_scripts" dev="dm-38" ino=57472 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:334): avc:  denied  { relabelfrom } for  name="abnormal.sh" dev="dm-38" ino=57473 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:335): avc:  denied  { relabelfrom } for  name="aosp.sh" dev="dm-38" ino=57474 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:336): avc:  denied  { relabelfrom } for  name="app.sh" dev="dm-38" ino=57475 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:337): avc:  denied  { relabelfrom } for  name="banned.sh" dev="dm-38" ino=57476 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:338): avc:  denied  { relabelfrom } for  name="boot_hash.sh" dev="dm-38" ino=57477 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:339): avc:  denied  { relabelfrom } for  name="derpfest.sh" dev="dm-38" ino=57478 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:340): avc:  denied  { relabelfrom } for  name="helluva.sh" dev="dm-38" ino=57479 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:341): avc:  denied  { relabelfrom } for  name="issue.sh" dev="dm-38" ino=57480 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:342): avc:  denied  { relabelfrom } for  name="keybox.sh" dev="dm-38" ino=57481 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:343): avc:  denied  { relabelfrom } for  name="kill.sh" dev="dm-38" ino=57482 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:344): avc:  denied  { relabelfrom } for  name="meowdump.sh" dev="dm-38" ino=57483 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:345): avc:  denied  { relabelfrom } for  name="meowverse.sh" dev="dm-38" ino=57484 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:346): avc:  denied  { relabelfrom } for  name="modal.sh" dev="dm-38" ino=57485 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:347): avc:  denied  { relabelfrom } for  name="module_info.sh" dev="dm-38" ino=57486 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:348): avc:  denied  { relabelfrom } for  name="patch.sh" dev="dm-38" ino=57487 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:349): avc:  denied  { relabelfrom } for  name="pif.sh" dev="dm-38" ino=57488 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:350): avc:  denied  { relabelfrom } for  name="piffork.sh" dev="dm-38" ino=57489 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:351): avc:  denied  { relabelfrom } for  name="pixelos.sh" dev="dm-38" ino=57490 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:352): avc:  denied  { relabelfrom } for  name="prop.sh" dev="dm-38" ino=57491 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:353): avc:  denied  { relabelfrom } for  name="resetprop.sh" dev="dm-38" ino=57492 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:354): avc:  denied  { relabelfrom } for  name="selinux.sh" dev="dm-38" ino=57493 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:355): avc:  denied  { relabelfrom } for  name="setprop.sh" dev="dm-38" ino=57494 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:356): avc:  denied  { relabelfrom } for  name="spoof.sh" dev="dm-38" ino=57495 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:357): avc:  denied  { relabelfrom } for  name="start.sh" dev="dm-38" ino=57496 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:358): avc:  denied  { relabelfrom } for  name="stop.sh" dev="dm-38" ino=57497 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:359): avc:  denied  { relabelfrom } for  name="sus.sh" dev="dm-38" ino=57498 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:360): avc:  denied  { relabelfrom } for  name="systemuser.sh" dev="dm-38" ino=57499 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.664     1     1 W init    : type=1400 audit(0.0:361): avc:  denied  { relabelfrom } for  name="user.sh" dev="dm-38" ino=57500 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:362): avc:  denied  { relabelfrom } for  name="vending.sh" dev="dm-38" ino=57501 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:363): avc:  denied  { relabelfrom } for  name="xiaomi.sh" dev="dm-38" ino=57502 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:364): avc:  denied  { relabelfrom } for  name="game" dev="dm-38" ino=57503 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:365): avc:  denied  { relabelfrom } for  name="Mona.otf" dev="dm-38" ino=57504 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:366): avc:  denied  { relabelfrom } for  name="image.png" dev="dm-38" ino=57505 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:367): avc:  denied  { relabelfrom } for  name="image2.png" dev="dm-38" ino=57506 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:368): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=57507 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:369): avc:  denied  { relabelfrom } for  name="meowverse.png" dev="dm-38" ino=57508 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:370): avc:  denied  { relabelfrom } for  name="script.js" dev="dm-38" ino=57509 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:371): avc:  denied  { relabelfrom } for  name="style.css" dev="dm-38" ino=57510 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:372): avc:  denied  { relabelfrom } for  name="index.html" dev="dm-38" ino=57511 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:373): avc:  denied  { relabelfrom } for  name="lang" dev="dm-38" ino=57512 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:374): avc:  denied  { relabelfrom } for  name="ar.js" dev="dm-38" ino=57513 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:375): avc:  denied  { relabelfrom } for  name="bn.js" dev="dm-38" ino=57514 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:376): avc:  denied  { relabelfrom } for  name="en.js" dev="dm-38" ino=57515 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:377): avc:  denied  { relabelfrom } for  name="es.js" dev="dm-38" ino=57516 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:378): avc:  denied  { relabelfrom } for  name="fr.js" dev="dm-38" ino=57517 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:379): avc:  denied  { relabelfrom } for  name="hi.js" dev="dm-38" ino=57518 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:380): avc:  denied  { relabelfrom } for  name="id.js" dev="dm-38" ino=57519 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:381): avc:  denied  { relabelfrom } for  name="pl.js" dev="dm-38" ino=57520 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:382): avc:  denied  { relabelfrom } for  name="pt.js" dev="dm-38" ino=57521 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:383): avc:  denied  { relabelfrom } for  name="ru.js" dev="dm-38" ino=57522 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:384): avc:  denied  { relabelfrom } for  name="ta.js" dev="dm-38" ino=57523 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:385): avc:  denied  { relabelfrom } for  name="te.js" dev="dm-38" ino=57524 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:386): avc:  denied  { relabelfrom } for  name="tr.js" dev="dm-38" ino=57525 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:387): avc:  denied  { relabelfrom } for  name="uk.js" dev="dm-38" ino=57526 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:388): avc:  denied  { relabelfrom } for  name="ur.js" dev="dm-38" ino=57527 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:389): avc:  denied  { relabelfrom } for  name="vi.js" dev="dm-38" ino=57528 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:390): avc:  denied  { relabelfrom } for  name="zh.js" dev="dm-38" ino=57529 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:391): avc:  denied  { relabelfrom } for  name="meowna.ttf" dev="dm-38" ino=57530 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.668     1     1 W init    : type=1400 audit(0.0:392): avc:  denied  { relabelfrom } for  name="mona.ttf" dev="dm-38" ino=57531 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.672     1     1 W init    : type=1400 audit(0.0:393): avc:  denied  { relabelfrom } for  name="script.js" dev="dm-38" ino=57532 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.672     1     1 W init    : type=1400 audit(0.0:394): avc:  denied  { relabelfrom } for  name="style.css" dev="dm-38" ino=57533 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.672     1     1 W init    : type=1400 audit(0.0:395): avc:  denied  { relabelfrom } for  name="target.txt" dev="dm-38" ino=42842 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:396): avc:  denied  { relabelfrom } for  name="keybox.xml.bak" dev="dm-38" ino=42841 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:397): avc:  denied  { relabelfrom } for  name="lspd" dev="dm-38" ino=50603 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:398): avc:  denied  { relabelfrom } for  name="config" dev="dm-38" ino=50604 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:399): avc:  denied  { relabelfrom } for  name="modules_config.db" dev="dm-38" ino=50616 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:400): avc:  denied  { relabelfrom } for  name="modules_config.db-wal" dev="dm-38" ino=50618 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:401): avc:  denied  { relabelfrom } for  name="modules_config.db-shm" dev="dm-38" ino=50619 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:402): avc:  denied  { relabelfrom } for  name="log" dev="dm-38" ino=57361 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:403): avc:  denied  { getattr } for  path="/data/adb/lspd/log/props.txt" dev="dm-38" ino=57362 scontext=u:r:init:s0 tcontext=u:object_r:app_data_file:s0 tclass=file permissive=0 bug=b/77873135
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:404): avc:  denied  { relabelfrom } for  name="kmsg.log" dev="dm-38" ino=57363 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:405): avc:  denied  { relabelfrom } for  name="verbose_2025-07-21T08:12:48.195945.log" dev="dm-38" ino=57364 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:406): avc:  denied  { relabelfrom } for  name="modules_2025-07-21T08:12:48.200258.log" dev="dm-38" ino=57365 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:407): avc:  denied  { relabelfrom } for  name="lock" dev="dm-38" ino=50607 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:408): avc:  denied  { relabelfrom } for  name="log.old" dev="dm-38" ino=56486 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=dir permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:409): avc:  denied  { getattr } for  path="/data/adb/lspd/log.old/props.txt" dev="dm-38" ino=56487 scontext=u:r:init:s0 tcontext=u:object_r:app_data_file:s0 tclass=file permissive=0 bug=b/77873135
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:410): avc:  denied  { relabelfrom } for  name="kmsg.log" dev="dm-38" ino=56488 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:411): avc:  denied  { relabelfrom } for  name="verbose_2025-07-21T08:08:43.597389.log" dev="dm-38" ino=56489 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:03.676     1     1 W init    : type=1400 audit(0.0:412): avc:  denied  { relabelfrom } for  name="modules_2025-07-21T08:08:43.598207.log" dev="dm-38" ino=56490 scontext=u:r:init:s0 tcontext=u:object_r:system_file:s0 tclass=file permissive=0
07-21 11:00:04.016   818   818 W init    : type=1400 audit(0.0:413): avc:  denied  { execute } for  name="ksud" dev="dm-38" ino=6999 scontext=u:r:init:s0 tcontext=u:object_r:adb_data_file:s0 tclass=file permissive=0
07-21 11:00:04.436   865   865 W vendor.qti.hard: type=1400 audit(0.0:414): avc:  denied  { read } for  name="input" dev="tmpfs" ino=1159 scontext=u:r:hal_vibrator_default:s0 tcontext=u:object_r:input_device:s0 tclass=dir permissive=0
07-21 11:00:04.456   878   878 W vendor.oplus.ha: type=1400 audit(0.0:415): avc:  denied  { read } for  name="u:object_r:system_prop:s0" dev="tmpfs" ino=10681 scontext=u:r:hal_performance_oplus:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:416): avc:  denied  { search } for  name="block" dev="tmpfs" ino=10254 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:417): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:418): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:419): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:420): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:421): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:422): avc:  denied  { search } for  name="oplusSensorFeature" dev="proc" ino=4026535179 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:423): avc:  denied  { read } for  name="Sensor" dev="proc" ino=4026534728 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=file permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:424): avc:  denied  { search } for  name="block" dev="tmpfs" ino=10254 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:425): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:426): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:427): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:428): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:429): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:430): avc:  denied  { search } for  name="als_cali" dev="proc" ino=4026535607 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_cali:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:431): avc:  denied  { search } for  name="block" dev="tmpfs" ino=10254 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:432): avc:  denied  { search } for  name="block" dev="tmpfs" ino=10254 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
07-21 11:00:04.472   876   876 W vendor.oplus.ha: type=1400 audit(0.0:433): avc:  denied  { search } for  name="block" dev="tmpfs" ino=10254 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
07-21 11:00:04.508   846   846 W android.hardwar: type=1400 audit(0.0:434): avc:  denied  { search } for  name="oplusSensorFeature" dev="proc" ino=4026535179 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=dir permissive=0
07-21 11:00:04.516   846   846 W android.hardwar: type=1400 audit(0.0:435): avc:  denied  { search } for  name="oplusSensorFeature" dev="proc" ino=4026535179 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=dir permissive=0
07-21 11:00:04.520   846   846 W android.hardwar: type=1400 audit(0.0:436): avc:  denied  { search } for  name="oplusSensorFeature" dev="proc" ino=4026535179 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=dir permissive=0
07-21 11:00:04.524   846   846 W android.hardwar: type=1400 audit(0.0:437): avc:  denied  { search } for  name="oplusSensorFeature" dev="proc" ino=4026535179 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:vendor_proc_oplus_sensor_feature:s0 tclass=dir permissive=0
07-21 11:00:04.528   846   846 W android.hardwar: type=1400 audit(0.0:438): avc:  denied  { read } for  name="m_virtual_sensor_misc" dev="tmpfs" ino=9467 scontext=u:r:hal_sensors_default:s0 tcontext=u:object_r:virtual_sensor_device:s0 tclass=chr_file permissive=0
07-21 11:00:04.540   924   924 W nvram_daemon: type=1400 audit(0.0:439): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:nvram_daemon:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.540   924   924 W nvram_daemon: type=1400 audit(0.0:440): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:nvram_daemon:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.548   924   924 W nvram_daemon: type=1400 audit(0.0:441): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:nvram_daemon:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.548   924   924 W nvram_daemon: type=1400 audit(0.0:442): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:nvram_daemon:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.620   881   881 W audioserver: type=1400 audit(0.0:443): avc:  denied  { read } for  name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=10721 scontext=u:r:audioserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=0
07-21 11:00:04.812   998   998 W fuelgauged_nvra: type=1400 audit(0.0:444): avc:  denied  { search } for  name="android" dev="sysfs" ino=36 scontext=u:r:fuelgauged_nvram:s0 tcontext=u:object_r:sysfs_dt_firmware_android:s0 tclass=dir permissive=0
07-21 11:00:04.812   998   998 W fuelgauged_nvra: type=1400 audit(0.0:445): avc:  denied  { search } for  name="android" dev="sysfs" ino=36 scontext=u:r:fuelgauged_nvram:s0 tcontext=u:object_r:sysfs_dt_firmware_android:s0 tclass=dir permissive=0
07-21 11:00:04.812   998   998 W fuelgauged_nvra: type=1400 audit(0.0:446): avc:  denied  { search } for  name="android" dev="sysfs" ino=36 scontext=u:r:fuelgauged_nvram:s0 tcontext=u:object_r:sysfs_dt_firmware_android:s0 tclass=dir permissive=0
07-21 11:00:04.812   998   998 W fuelgauged_nvra: type=1400 audit(0.0:447): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:fuelgauged_nvram:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.812   998   998 W fuelgauged_nvra: type=1400 audit(0.0:448): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:fuelgauged_nvram:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:04.920  1031  1031 W thermal_manager: type=1400 audit(0.0:449): avc:  denied  { read } for  name="u:object_r:vendor_thermal_prop:s0" dev="tmpfs" ino=10954 scontext=u:r:thermal_manager:s0 tcontext=u:object_r:vendor_thermal_prop:s0 tclass=file permissive=0
07-21 11:00:04.932   881   881 W audioserver: type=1400 audit(0.0:450): avc:  denied  { read } for  name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=10721 scontext=u:r:audioserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=0
07-21 11:00:04.944   881   881 W audioserver: type=1400 audit(0.0:451): avc:  denied  { read } for  name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=10721 scontext=u:r:audioserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=0
07-21 11:00:05.004  1154  1154 W init    : type=1400 audit(0.0:452): avc:  denied  { execute } for  name="ksud" dev="dm-38" ino=6999 scontext=u:r:init:s0 tcontext=u:object_r:adb_data_file:s0 tclass=file permissive=0
07-21 11:00:05.008   903   903 W ccci_mdinit: type=1400 audit(0.0:453): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:ccci_mdinit:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.008   903   903 W ccci_mdinit: type=1400 audit(0.0:454): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:ccci_mdinit:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.012   903   903 W ccci_mdinit: type=1400 audit(0.0:455): avc:  denied  { read } for  name="sdc3" dev="tmpfs" ino=11281 scontext=u:r:ccci_mdinit:s0 tcontext=u:object_r:oplus_block_device:s0 tclass=blk_file permissive=0
07-21 11:00:05.040  1060  1060 W vendor.mediatek: type=1400 audit(0.0:456): avc:  denied  { read } for  name="lcd" dev="proc" ino=4026534732 scontext=u:r:mtk_hal_pq:s0 tcontext=u:object_r:vendor_proc_display:s0 tclass=file permissive=0
07-21 11:00:05.040  1060  1060 W vendor.mediatek: type=1400 audit(0.0:457): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_pq:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.080   942   942 W McDaemon.FSD2: type=1400 audit(0.0:458): avc:  denied  { read } for  name="u:object_r:system_prop:s0" dev="tmpfs" ino=10681 scontext=u:r:mobicore:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=0
07-21 11:00:05.080   942   942 W McDaemon.FSD2: type=1400 audit(0.0:459): avc:  denied  { read } for  name="u:object_r:system_oplus_project_prop:s0" dev="tmpfs" ino=10680 scontext=u:r:mobicore:s0 tcontext=u:object_r:system_oplus_project_prop:s0 tclass=file permissive=0
07-21 11:00:05.088  1060  1060 W vendor.mediatek: type=1400 audit(0.0:460): avc:  denied  { read } for  name="lcd" dev="proc" ino=4026534732 scontext=u:r:mtk_hal_pq:s0 tcontext=u:object_r:vendor_proc_display:s0 tclass=file permissive=0
07-21 11:00:05.100  1060  1060 W AALMain : type=1400 audit(0.0:461): avc:  denied  { read } for  name="lcd" dev="proc" ino=4026534732 scontext=u:r:mtk_hal_pq:s0 tcontext=u:object_r:vendor_proc_display:s0 tclass=file permissive=0
07-21 11:00:05.488   994   994 W vtservice: type=1400 audit(0.0:462): avc:  denied  { read } for  name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=10721 scontext=u:r:vtservice:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=0
07-21 11:00:05.508   915   915 W ccci_rpcd: type=1400 audit(0.0:463): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:ccci_rpcd:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.508   915   915 W ccci_rpcd: type=1400 audit(0.0:464): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:ccci_rpcd:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.508   915   915 W ccci_rpcd: type=1400 audit(0.0:465): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:ccci_rpcd:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.568  1025  1025 W camerahalserver: type=1400 audit(0.0:466): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.568  1025  1025 W camerahalserver: type=1400 audit(0.0:467): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.568  1025  1025 W camerahalserver: type=1400 audit(0.0:468): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.568  1025  1025 W camerahalserver: type=1400 audit(0.0:469): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:470): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:471): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:472): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:473): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:474): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:475): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:476): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:477): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:478): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:479): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:480): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:481): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:482): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:483): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:484): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:485): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:486): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:487): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:488): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:489): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:490): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:491): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:492): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:493): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:494): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:495): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:496): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:497): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:498): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:499): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:500): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604  1025  1025 W camerahalserver: type=1400 audit(0.0:501): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.604     1     1 W /system/bin/init: type=1107 audit(0.0:502): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:05.608  1025  1025 W camerahalserver: type=1400 audit(0.0:503): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.608  1025  1025 W camerahalserver: type=1400 audit(0.0:504): avc:  denied  { search } for  name="oplusVersion" dev="proc" ino=4026531911 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_proc_oplus_version:s0 tclass=dir permissive=0
07-21 11:00:05.608     1     1 W /system/bin/init: type=1107 audit(0.0:505): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.project pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:506): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:507): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:508): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:509): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:510): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:06.796  1025  1025 W camerahalserver: type=1400 audit(0.0:511): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.008  1025  1025 W camerahalserver: type=1400 audit(0.0:512): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.012  1025  1025 W camerahalserver: type=1400 audit(0.0:513): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.012  1025  1025 W camerahalserver: type=1400 audit(0.0:514): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:515): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:516): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:517): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:518): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:519): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.448  1025  1025 W camerahalserver: type=1400 audit(0.0:520): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.472  1407  1407 W RfxSender_1: type=1400 audit(0.0:521): avc:  denied  { read } for  name="u:object_r:default_prop:s0" dev="tmpfs" ino=10390 scontext=u:r:rild:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=0
07-21 11:00:07.604  1025  1025 W camerahalserver: type=1400 audit(0.0:522): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.604  1025  1025 W camerahalserver: type=1400 audit(0.0:523): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.604  1025  1025 W camerahalserver: type=1400 audit(0.0:524): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.872     1     1 W /system/bin/init: type=1107 audit(0.0:525): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.low_ram pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:07.876     1     1 W /system/bin/init: type=1107 audit(0.0:526): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.low_ram pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:07.876     1     1 W /system/bin/init: type=1107 audit(0.0:527): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.low_ram pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:07.884     1     1 W /system/bin/init: type=1107 audit(0.0:528): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.low_ram pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:07.912  1025  1025 W camerahalserver: type=1400 audit(0.0:529): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.912  1025  1025 W camerahalserver: type=1400 audit(0.0:530): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.912     1     1 W /system/bin/init: type=1107 audit(0.0:531): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.oplus.camera.low_ram pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:exported_system_prop:s0 tclass=property_service permissive=0'
07-21 11:00:07.912  1025  1025 W camerahalserver: type=1400 audit(0.0:532): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.916  1025  1025 W camerahalserver: type=1400 audit(0.0:533): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.916  1025  1025 W camerahalserver: type=1400 audit(0.0:534): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.916  1025  1025 W camerahalserver: type=1400 audit(0.0:535): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.916  1025  1025 W camerahalserver: type=1400 audit(0.0:536): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.916  1025  1025 W camerahalserver: type=1400 audit(0.0:537): avc:  denied  { search } for  name="/" dev="sdc16" ino=2 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:persist_data_file:s0 tclass=dir permissive=0
07-21 11:00:07.944     1     1 W /system/bin/init: type=1107 audit(0.0:538): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.debug.tpi.s.semi.run pid=1025 uid=1047 gid=1005 scontext=u:r:mtk_hal_camera:s0 tcontext=u:object_r:vendor_oplus_prop:s0 tclass=property_service permissive=0'
07-21 11:00:12.072   633   633 W binder:633_1: type=1400 audit(0.0:539): avc:  denied  { read } for  name="wakeup169" dev="sysfs" ino=54376 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=0
07-21 11:00:12.828  1009  1009 W binder:1009_1: type=1400 audit(0.0:540): avc:  denied  { read } for  name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=10721 scontext=u:r:mediaserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=0