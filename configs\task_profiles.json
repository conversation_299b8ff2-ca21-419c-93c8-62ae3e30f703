{"Attributes": [{"Name": "LowCapacityCPUs", "Controller": "cpuset", "File": "background/cpus"}, {"Name": "HighCapacityCPUs", "Controller": "cpuset", "File": "foreground/cpus"}, {"Name": "MaxCapacityCPUs", "Controller": "cpuset", "File": "top-app/cpus"}, {"Name": "MemLimit", "Controller": "memory", "File": "memory.limit_in_bytes"}, {"Name": "MemSoftLimit", "Controller": "memory", "File": "memory.soft_limit_in_bytes"}, {"Name": "MemSwappiness", "Controller": "memory", "File": "memory.swappiness"}, {"Name": "STuneBoost", "Controller": "schedtune", "File": "schedtune.boost"}, {"Name": "STunePreferIdle", "Controller": "schedtune", "File": "schedtune.prefer_idle"}, {"Name": "UClampMin", "Controller": "schedtune", "File": "schedtune.util.min"}, {"Name": "UClampMax", "Controller": "schedtune", "File": "schedtune.util.max"}, {"Name": "FreezerState", "Controller": "freezer", "File": "frozen/freezer.state"}], "Profiles": [{"Name": "HighEnergySaving", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": "background"}}]}, {"Name": "Frozen", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "freezer", "Path": "frozen"}}]}, {"Name": "Unf<PERSON>zen", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "freezer", "Path": ""}}]}, {"Name": "NormalPerformance", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": ""}}]}, {"Name": "HighPerformance", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": "foreground"}}]}, {"Name": "MaxPerformance", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": "top-app"}}]}, {"Name": "RealtimePerformance", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": "rt"}}]}, {"Name": "CameraServicePerformance", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "schedtune", "Path": "camera-daemon"}}]}, {"Name": "CpuPolicySpread", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "STunePreferIdle", "Value": "1"}}]}, {"Name": "CpuPolicyPack", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "STunePreferIdle", "Value": "0"}}]}, {"Name": "VrKernelCapacity", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": ""}}]}, {"Name": "VrServiceCapacityLow", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "system/background"}}]}, {"Name": "VrServiceCapacityNormal", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "system"}}]}, {"Name": "VrServiceCapacityHigh", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "system/performance"}}]}, {"Name": "VrProcessCapacityLow", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "application/background"}}]}, {"Name": "VrProcessCapacityNormal", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "application"}}]}, {"Name": "VrProcessCapacityHigh", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "application/performance"}}]}, {"Name": "ProcessCapacityLow", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "background"}}]}, {"Name": "ProcessCapacityNormal", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": ""}}]}, {"Name": "ProcessCapacityHigh", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "foreground"}}]}, {"Name": "ProcessCapacityMax", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "top-app"}}]}, {"Name": "ServiceCapacityLow", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "system-background"}}]}, {"Name": "ServiceCapacityRestricted", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "restricted"}}]}, {"Name": "CameraServiceCapacity", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "cpuset", "Path": "camera-daemon"}}]}, {"Name": "LowIoPriority", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "blkio", "Path": "background"}}]}, {"Name": "NormalIoPriority", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "blkio", "Path": ""}}]}, {"Name": "HighIoPriority", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "blkio", "Path": ""}}]}, {"Name": "MaxIoPriority", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "blkio", "Path": ""}}]}, {"Name": "Timer<PERSON><PERSON><PERSON><PERSON><PERSON>", "Actions": [{"Name": "SetTimerSlack", "Params": {"Slack": "50000"}}]}, {"Name": "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Actions": [{"Name": "SetTimerSlack", "Params": {"Slack": "50000"}}]}, {"Name": "PerfBoost", "Actions": [{"Name": "SetClamps", "Params": {"Boost": "50%", "Clamp": "0"}}]}, {"Name": "PerfClamp", "Actions": [{"Name": "SetClamps", "Params": {"Boost": "0", "Clamp": "30%"}}]}, {"Name": "LowMemoryUsage", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "MemSoftLimit", "Value": "16MB"}}, {"Name": "SetAttribute", "Params": {"Name": "MemSwappiness", "Value": "150"}}]}, {"Name": "HighMemoryUsage", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "MemSoftLimit", "Value": "512MB"}}, {"Name": "SetAttribute", "Params": {"Name": "MemSwappiness", "Value": "100"}}]}, {"Name": "SystemMemoryProcess", "Actions": [{"Name": "JoinCgroup", "Params": {"Controller": "memory", "Path": "system"}}]}, {"Name": "FreezerThawed", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "FreezerState", "Value": "THAWED"}}]}, {"Name": "FreezerFrozen", "Actions": [{"Name": "SetAttribute", "Params": {"Name": "FreezerState", "Value": "FROZEN"}}]}], "AggregateProfiles": [{"Name": "SCHED_SP_DEFAULT", "Profiles": ["Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "SCHED_SP_BACKGROUND", "Profiles": ["HighEnergySaving", "LowIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "SCHED_SP_FOREGROUND", "Profiles": ["HighPerformance", "HighIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "SCHED_SP_TOP_APP", "Profiles": ["MaxPerformance", "MaxIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "SCHED_SP_RT_APP", "Profiles": ["RealtimePerformance", "MaxIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_DEFAULT", "Profiles": ["Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_BACKGROUND", "Profiles": ["HighEnergySaving", "ProcessCapacityLow", "LowIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_FOREGROUND", "Profiles": ["HighPerformance", "ProcessCapacityHigh", "HighIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_TOP_APP", "Profiles": ["MaxPerformance", "ProcessCapacityMax", "MaxIoPriority", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_SYSTEM", "Profiles": ["ServiceCapacityLow", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"Name": "CPUSET_SP_RESTRICTED", "Profiles": ["ServiceCapacityRestricted", "Timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}]}